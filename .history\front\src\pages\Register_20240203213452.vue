<template>
    <div class="register-form-container">
      <a-button @click="goToLogin" type="default" class="back-to-login-btn"><ArrowLeftOutlined :style="{ fontSize: '24px' }"/></a-button>
      <div class="register-form">
        <h1 class="page-title">用户注册</h1>
        <a-input v-model:value="username" placeholder="用户名" class="ant-input"></a-input>
        <a-input-password v-model:value="password" :class="{ 'error-input': passwordMismatch }" placeholder="密码" class="ant-input"></a-input-password>
        <a-input-password v-model:value="confirmPassword" :class="{ 'error-input': passwordMismatch }" placeholder="确认密码" class="ant-input"></a-input-password>
        <a-input v-model:value="email" placeholder="邮箱" class="ant-input"></a-input>
        <div class="verification-code-container">
          <a-input v-model:value="verificationCode" placeholder="验证码" class="ant-input"></a-input>
          <a-button @click="sendVerificationCode" type="default" class="ant-btn-send">发送验证码</a-button>
        </div>
        <a-button @click="register" type="primary" class="ant-btn-regis">注册</a-button>
      </div>
    </div>
  </template>
  
  <script>
  import { ArrowLeftOutlined } from '@ant-design/icons-vue';
  export default {
    name: 'RegisterView',
    components: {
        ArrowLeftOutlined
    },
    data() {
      return {
        username: '',
        password: '',
        confirmPassword: '',
        email: '',
        verificationCode: '',
        trueCode: ''
      };
    },
    // 刚切换到注册页面时，清空输入框
    beforeRouteLeave(to, from, next) {
      this.username = '';
      this.password = '';
      this.confirmPassword = '';
      this.email = '';
      this.verificationCode = '';
      this.trueCode = '';
      next();
    },
    computed: {
      passwordMismatch() {
        return this.password !== this.confirmPassword;
      }
    },
    methods: {
      sendVerificationCode() {
        // 实现发送验证码的逻辑
        console.log('发送验证码到邮箱:', this.email);
        this.$axios.post('/sendVerificationCode', {
          email: this.email
        }).then((res) => {
          if (res.data.code === 200) {
            this.trueCode=res.data.verificationCode;
            this.$message.success('验证码发送成功');
          } else {
            console.error('验证码发送失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('验证码发送失败:', err);
        });
      },
      register() {
        // 实现注册逻辑
        console.log('用户名:', this.username);
        console.log('密码:', this.password);
        console.log('确认密码:', this.confirmPassword);
        console.log('邮箱:', this.email);
        console.log('邮箱验证码:', this.verificationCode);
  
        if (this.passwordMismatch) {
          // 处理密码不匹配的情况，可以显示提示信息或者采取其他操作
          this.$message.error('两次输入的密码不一致');
          // 清空确认密码输入框
          this.confirmPassword = '';
          return;
        }
        if(this.verificationCode!==this.trueCode){
          this.$message.error('验证码错误');
          // 清空验证码输入框
          this.verificationCode = '';
          return;
        }
        // 发送注册请求
        this.$axios.post('/register', {
          username: this.username,
          password: this.password,
          email: this.email
        }).then((res) => {
          if (res.data.code === 200) {
            this.$message.success('注册成功');
            // 注册成功后，可以将用户重定向到登录页面
            this.$router.push('/');
          } else {
            this.$message.error('注册失败:' + res.data.message);
          }
        }).catch((err) => {
          console.error('注册失败:', err);
          this.$message.error('注册失败，未知错误!');
        });
  
      },
      goToLogin() {
        // 点击返回登录按钮时的逻辑，重定向到登录页面
        this.$router.push('/');
      }
    }
  };
  </script>
  
  <style scoped>
  /* 根据需要自定义样式 */
  .register-form-container {
    width: 480px;
    margin: 0 auto;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #ffffff;
    opacity: 0.99;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;

  }
  .page-title {
    text-align: center;
    font-size: 40px;
    margin: 30px;
  }
  
  .ant-input {
    margin: 8px; /* 为输入框添加底部间距 */
    font-size: 24px;
    width:300px;
  }
  .back-to-login-btn {
    position: absolute;
    top: 0;
    left: 0;
    margin: 20px;
    border: 0px!important;
  }
  
  .ant-btn-regis {
    width: 100%;
    font-size: 28px;
    height: 60px;
    width: 200px;
    margin: 20px;
  }
  .register-form {
    width: 300px; /* 调整表单宽度，根据需要进行修改 */
  }
  
  .verification-code-container {
    display: flex;
    align-items: center;
  }

  .ant-btn-send {
    margin-left: 10px;
    /* 高度和左侧输入框等高 */
    height: 46px;
  }
  
  .error-input {
    border-color: red;
  }
  
  /* 可以根据需要添加其他样式 */
  </style>
  