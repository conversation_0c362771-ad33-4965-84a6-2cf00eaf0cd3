import requests
import time

def send_request(url):
    try:
        # 发起 GET 请求到 /scan 接口
        response = requests.get(url + '/scan')
        response.raise_for_status()  # 检查请求是否成功

        # 提取任务ID
        task_id = response.json().get('task_id')

        print("请求成功，任务已启动。等待任务完成...")
        print(f"任务ID：{task_id}")
        # 检查任务状态
        st=time.time()
        while True:
            status_response = requests.get(url + f'/status/{task_id}')
            status_data = status_response.json()
            # 当前已等待时间
            print(time.time()-st)
            if status_data['status'] == 'Task completed':
                print(f"任务完成，响应内容：{status_data['result']}")
                break
            elif status_data['status'] == 'Task failed':
                print(f"任务失败，错误信息：{status_data['error']}")
                break
            else:
                print(f"当前任务状态：{status_data['status']}. 等待中...")
                time.sleep(1)

    except requests.exceptions.RequestException as e:
        print(f"请求发生异常：{e}")

# 直接向0244de2c-7489-44c1-956c-7e1057bf7ae7这个任务ID发送请求
def sendForID(url,task_id):
    while True:
        status_response = requests.get(url + f'/status/{task_id}')
        status_data = status_response.json()

        if status_data['status'] == 'Task completed':
            print(f"任务完成，响应内容：{status_data['result']}")
            break
        elif status_data['status'] == 'Task failed':
            print(f"任务失败，错误信息：{status_data['error']}")
            break
        else:
            print(f"当前任务状态：{status_data['status']}. 等待中...")
            time.sleep(1)
    
if __name__ == "__main__":
    # 替换成你的 Flask 接口地址
    api_url = "http://127.0.0.1:5000"  # 本地测试地址

    send_request(api_url)
    # sendForID(api_url,"0244de2c-7489-44c1-956c-7e1057bf7ae7")
