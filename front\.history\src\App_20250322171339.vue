<template>
  <div id="app">
    <router-view v-slot="{ Component }">
   <transition name="fade" mode="out-in" appear>
      <keep-alive>
         <component :is="Component" />
      </keep-alive>
   </transition>
</router-view>
  </div>
</template>

<script>
export default {
  name: 'App',
  setup() {
    // 在这里你可以做一些全局的配置或初始化
    return {};
  },
};
</script>

<style>
@import url('./reset.css');
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  text-align: center;
  color: #2c3e50;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* background-image: linear-gradient(-90deg, #29bdd9 0%, #276ace 100%); */
  /* 背景图片设置为assets下的wallhaven-428mey.jpg ，并等比缩放图片*/
  background: url('./assets/bg.png') no-repeat center center/cover;
}

/* 路由切换动画 */
/* fade-transform */
.fade-leave-active,
.fade-enter-active {
  transition: all 0.5s;
}
 
/* 可能为enter失效，拆分为 enter-from和enter-to */
.fade-enter-from {  
  opacity: 0;
  transform: translateY(-30px);
}
.fade-enter-to { 
  opacity: 1;
  transform: translateY(0px);
}
 
.fade-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

</style>
