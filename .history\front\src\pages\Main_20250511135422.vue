<template>
  <div class="main-container">
    <div class="main-header">
      <div class="user-info fade-in">
        <a-avatar :size="40" icon="user">
          <template #icon><UserOutlined /></template>
        </a-avatar>
        <span class="username">{{ $store.state.username }}</span>
      </div>
      <a-button @click="goToLogin" type="text" class="logout-button fade-in">
        <LogoutOutlined />
        退出登录
      </a-button>
    </div>

    <div class="main-content">
      <h1 class="welcome-title slide-in-up">欢迎使用智能视频检索系统</h1>
      <p class="welcome-subtitle slide-in-up delay-1">基于AI的视频内容定位平台</p>

      <div class="feature-cards">
        <div class="feature-card slide-in-up delay-2 hover-lift" @click="goToUserManagement">
          <div class="card-icon">
            <ContactsFilled />
          </div>
          <h3>个人信息</h3>
          <p>查看和管理您的个人资料</p>
        </div>

        <div class="feature-card slide-in-up delay-3 hover-lift" @click="goToTaskManagement">
          <div class="card-icon">
            <ProfileFilled />
          </div>
          <h3>会话管理</h3>
          <p>创建和管理您的视频分析任务</p>
        </div>
      </div>
    </div>

    <div class="main-footer fade-in delay-5">
      <p>© 2025 智能视频检索系统 - 版权所有</p>
    </div>
  </div>
</template>

<script>
import { ContactsFilled, ProfileFilled, UserOutlined, LogoutOutlined } from '@ant-design/icons-vue';

export default {
  name: 'MainView',
  components: {
    ContactsFilled,
    ProfileFilled,
    UserOutlined,
    LogoutOutlined
  },
  methods: {
    goToUserManagement() {
      this.$router.push('/user');
    },
    goToTaskManagement() {
      this.$router.push('/task');
    },
    goToLogin() {
      this.$message.success('已安全退出系统');
      this.$router.push('/');
    }
  },
};
</script>

<style scoped>
.main-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.username {
  font-weight: 500;
  font-size: 16px;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ff4d4f;
  transition: all 0.3s;
}

.logout-button:hover {
  color: #ff7875;
  transform: translateY(-2px);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  text-align: center;
}

.welcome-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 10px;
  color: #333;
  background: linear-gradient(to right, #4776E6, #8E54E9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.welcome-subtitle {
  font-size: 18px;
  color: #666;
  margin-bottom: 50px;
}

.feature-cards {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.feature-card {
  width: 280px;
  padding: 30px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 40px;
  color: #1890ff;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.feature-card p {
  color: #666;
  font-size: 16px;
}

.main-footer {
  padding: 20px;
  text-align: center;
  color: #666;
  background-color: white;
  border-top: 1px solid #eee;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .main-header {
    padding: 15px 20px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .feature-cards {
    flex-direction: column;
    gap: 20px;
  }

  .feature-card {
    width: 100%;
  }
}
</style>
