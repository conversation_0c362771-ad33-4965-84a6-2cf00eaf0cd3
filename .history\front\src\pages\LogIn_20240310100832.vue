<template>
    <div class="login-form">
      <h1 class="page-title">智能视频检索系统</h1>
      <a-input v-model:value="username" placeholder="用户名" class="ant-input"></a-input>
      <a-input-password v-model:value="password" placeholder="密码" class="ant-input"></a-input-password>
      <div class="login-form-btns">
        <a-button @click="login" type="primary" class="ant-btn">登录</a-button>
        <a-button @click="register" type="default" class="ant-btn">注册</a-button>
        </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'LogInView',
    data() {
      return {
        username: '',
        password: ''
      };
    },
    beforeRouteLeave(to, from, next) {
      this.username = '';
      this.password = '';
      next();
    },
    methods: {
      login() {
        console.log('用户名:', this.username);
        console.log('密码:', this.password);
        // 设置登录状态
        // 发送登录请求
        this.$axios.post('/login', {
          username: this.username,
          password: this.password
        }).then((res) => {
          console.log('登录成功:', res.data);
          if(res.data.code === 200) {
            // 登录成功后，将用户信息保存到vuex中
            this.$store.dispatch('updateUsername', res.data.user.username);
            this.$store.dispatch('updatePassword', res.data.user.password);
            this.$store.dispatch('updateEmail', res.data.user.email);
            this.$store.dispatch('updateIsAdmin', res.data.user.is_admin);
            // 跳转到首页
            this.$router.push('/main');
          }else{
            // 登录失败，提示错误信息
            this.$message.error(res.data.message);
          }
        }).catch((err) => {
          console.error('登录失败:', err);
        });

      },
      register() {
        // 跳转到注册页面
        this.$router.push('/register');
      }
    }
  };
  </script>
  
  <style scoped>
  .login-form {
    width: 600px;
    margin: 0 auto;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #ffffff;
    opacity: 0.99;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .login-form-btns {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .page-title {
    text-align: center;
    font-size: 48px;
    margin: 30px;
    color:#fefefe;
    text-shadow:0px 1px 0px #c0c0c0,
    0px 2px 0px #b0b0b0,
    0px 3px 0px #a0a0a0,
    0px 4px 0px #909090,
    0px 5px 10px rgba(0, 0, 0, .9);
    letter-spacing: 2px;
  }
  
  .ant-input {
    margin-bottom: 16px; /* 为输入框添加底部间距 */
    font-size: 24px;
    width:300px;
  }
  
  .ant-btn {
    width: 100%;
    font-size: 28px;
    height: 60px;
    width: 200px;
    margin: 20px;
  }
  </style>
  