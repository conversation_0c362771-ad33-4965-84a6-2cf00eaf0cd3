<template>
  <div class="main-container">
    <div class="main-header">
      <div class="user-info fade-in">
        <a-avatar :size="40" icon="user">
          <template #icon><UserOutlined /></template>
        </a-avatar>
        <span class="username">{{ $store.state.username }}</span>
      </div>
      <a-button @click="goToLogin" type="text" class="logout-button fade-in">
        <LogoutOutlined />
        退出登录
      </a-button>
    </div>

    <div class="main-content">
      <h1 class="welcome-title slide-in-up">城市智能治理平台</h1>
      <p class="welcome-subtitle slide-in-up delay-1">多模态检测技术助力城市精细化管理</p>

      <div class="theme-stats slide-in-up delay-2">
        <div class="stat-item">
          <div class="stat-value">98.5%</div>
          <div class="stat-label">检测准确率</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">24/7</div>
          <div class="stat-label">全天候监测</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">5+</div>
          <div class="stat-label">检测模态</div>
        </div>
      </div>

      <div class="feature-cards">
        <div class="feature-card slide-in-up delay-3 hover-lift" @click="goToUserManagement">
          <div class="card-icon">
            <ContactsFilled />
          </div>
          <h3>个人中心</h3>
          <p>管理您的账户信息和权限设置</p>
        </div>

        <div class="feature-card slide-in-up delay-4 hover-lift" @click="goToTaskManagement">
          <div class="card-icon">
            <ProfileFilled />
          </div>
          <h3>监测任务</h3>
          <p>创建和管理城市监测分析任务</p>
        </div>
      </div>
    </div>

    <div class="main-footer fade-in delay-5">
      <div class="footer-content">
        <p>© 2025 城市智能治理平台 - 多模态检测技术助力城市精细化管理</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ContactsFilled, ProfileFilled, UserOutlined, LogoutOutlined, AreaChartOutlined } from '@ant-design/icons-vue';

export default {
  name: 'MainView',
  components: {
    ContactsFilled,
    ProfileFilled,
    UserOutlined,
    LogoutOutlined,
    AreaChartOutlined
  },
  methods: {
    goToUserManagement() {
      this.$router.push('/user');
    },
    goToTaskManagement() {
      this.$router.push('/task');
    },
    goToLogin() {
      this.$message.success('已安全退出系统');
      this.$router.push('/');
    }
  },
};
</script>

<style scoped>
.main-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.username {
  font-weight: 500;
  font-size: 16px;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ff4d4f;
  transition: all 0.3s;
}

.logout-button:hover {
  color: #ff7875;
  transform: translateY(-2px);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 20px;
  text-align: center;
}

.welcome-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 10px;
  color: #1890ff;
}

.welcome-subtitle {
  font-size: 18px;
  color: #666;
  margin-bottom: 30px;
}

.theme-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 40px;
}

.stat-item {
  text-align: center;
  padding: 15px 25px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  min-width: 120px;
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.feature-cards {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.feature-card {
  width: 280px;
  padding: 30px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(24, 144, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.feature-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('../assets/decorative_pattern.svg') no-repeat center center;
  background-size: 80%;
  opacity: 0.03;
  z-index: 0;
  pointer-events: none;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 40px;
  color: #1890ff;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.feature-card p {
  color: #666;
  font-size: 16px;
}

.main-footer {
  padding: 20px;
  color: #666;
  background-color: white;
  border-top: 1px solid #eee;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-links a {
  color: #666;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .main-header {
    padding: 15px 20px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .theme-stats {
    flex-direction: column;
    gap: 15px;
  }

  .stat-item {
    width: 100%;
  }

  .feature-cards {
    flex-direction: column;
    gap: 20px;
  }

  .feature-card {
    width: 100%;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>
