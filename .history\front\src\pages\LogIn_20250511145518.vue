<template>
  <div class="login-container">
    <div class="login-card fade-in">
      <div class="login-header">
        <h1 class="login-title">城市智能治理平台</h1>
        <p class="login-subtitle">多模态检测技术助力城市精细化管理</p>
        <div class="theme-badges">
          <span class="theme-badge">视频分析</span>
          <span class="theme-badge">智能监测</span>
          <span class="theme-badge">城市治理</span>
        </div>
      </div>
      <div class="login-form">
        <a-input
          v-model:value="username"
          placeholder="用户名"
          size="large"
          class="login-input slide-in-up delay-1"
        >
          <template #prefix><user-outlined /></template>
        </a-input>

        <a-input-password
          v-model:value="password"
          placeholder="密码"
          size="large"
          class="login-input slide-in-up delay-2"
        >
          <template #prefix><lock-outlined /></template>
        </a-input-password>

        <div class="login-actions">
          <a-button
            @click="login"
            type="primary"
            size="large"
            class="login-button slide-in-up delay-3"
            :loading="loading"
          >
            登录
          </a-button>
          <a-button
            @click="register"
            type="default"
            size="large"
            class="register-button slide-in-up delay-4"
          >
            注册
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';

export default {
  name: 'LogInView',
  components: {
    UserOutlined,
    LockOutlined
  },
  data() {
    return {
      username: '',
      password: '',
      loading: false
    };
  },
  beforeRouteLeave(to, from, next) {
    this.username = '';
    this.password = '';
    next();
  },
  methods: {
    login() {
      if (!this.username || !this.password) {
        this.$message.warning('请输入用户名和密码');
        return;
      }

      this.loading = true;

      // 发送登录请求
      this.$axios.post('/login', {
        username: this.username,
        password: this.password
      }).then((res) => {
        if(res.data.code === 200) {
          this.$message.success('登录成功，欢迎回来！');

          // 登录成功后，将用户信息保存到vuex中
          this.$store.dispatch('updateUsername', res.data.user.username);
          this.$store.dispatch('updatePassword', res.data.user.password);
          this.$store.dispatch('updateEmail', res.data.user.email);
          this.$store.dispatch('updateIsAdmin', res.data.user.is_admin);

          // 跳转到首页
          this.$router.push('/main');
        } else {
          // 登录失败，提示错误信息
          this.$message.error(res.data.message || '登录失败，请检查用户名和密码');
        }
      }).catch((err) => {
        console.error('登录失败:', err);
        this.$message.error('登录请求失败，请稍后再试');
      }).finally(() => {
        this.loading = false;
      });
    },
    register() {
      // 跳转到注册页面
      this.$router.push('/register');
    }
  }
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: url('../assets/pattern_bg.svg') repeat;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('../assets/decorative_pattern.svg') no-repeat center center;
  z-index: 0;
  opacity: 0.3;
}

.login-card {
  width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(24, 144, 255, 0.2);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.login-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: #1890ff;
  opacity: 0.8;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-title {
  font-size: 32px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
  background: linear-gradient(to right, #1890ff, #52c41a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.login-subtitle {
  color: #666;
  font-size: 16px;
  margin-bottom: 15px;
}

.theme-badges {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;
}

.theme-badge {
  display: inline-block;
  padding: 4px 12px;
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border-radius: 16px;
  font-size: 12px;
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.login-form {
  display: flex;
  flex-direction: column;
}

.login-input {
  margin-bottom: 20px;
  height: 50px;
  border-radius: 8px;
}

.login-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.login-button, .register-button {
  height: 50px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s;
}

.login-button {
  background: linear-gradient(to right, #1890ff, #52c41a);
  border: none;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(24, 144, 255, 0.2), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.register-button:hover {
  background-color: #f0f0f0;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .login-card {
    width: 90%;
    padding: 30px 20px;
  }

  .login-title {
    font-size: 28px;
  }
}
</style>
