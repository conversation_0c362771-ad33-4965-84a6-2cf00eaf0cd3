// router/index.js
import { createRouter, createWebHistory } from 'vue-router';
import LogInView from '../pages/LogIn.vue';
import RegisterView from '../pages/Register.vue';
import MainView from '../pages/Main.vue';
import UserView from '../pages/User.vue';
import TaskView from '../pages/Task.vue';

import MWTaskView from '../methods/Task_MW.vue';

const routes = [
  { path: '/', component: LogInView },
  { path: '/register', component: RegisterView },
  {path:'/main',component:MainView},
  {path:'/user',component:UserView},
  {path:'/task',component:TaskView},
  {path:'/task/balanced',component:MWTaskView},
  // 其他路由...
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
