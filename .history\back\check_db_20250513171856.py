import sqlite3
import json
import os

# 打印当前工作目录
print(f"当前工作目录: {os.getcwd()}")

# 检查数据库文件是否存在
db_path = 'DB/database.db'
if os.path.exists(db_path):
    print(f"数据库文件存在: {db_path}")
else:
    print(f"数据库文件不存在: {db_path}")
    # 尝试查找数据库文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file == 'database.db':
                print(f"找到数据库文件: {os.path.join(root, file)}")

# 连接到数据库
conn = sqlite3.connect('DB/database.db')
cursor = conn.cursor()

# 查询所有任务
print("所有任务:")
cursor.execute("SELECT * FROM tasks")
tasks = cursor.fetchall()
for task in tasks:
    print(f"ID: {task[0]}, 用户名: {task[1]}, 任务号: {task[2]}, 更新时间: {task[3]}, 创建时间: {task[4]}, 模式: {task[5]}, 状态: {task[6]}, 描述: {task[8]}")
    print(f"配置: {task[7]}")
    print("-" * 80)

# 关闭连接
conn.close()
