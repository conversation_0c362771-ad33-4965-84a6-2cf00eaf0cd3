import requests
import json

def test_rwscan():
    url = 'http://localhost:5000/RWScan'
    data = {
        'task_id': 1,
        'task_config': {
            'prompt': 'test',
            'video_name': 'test.mp4',
            'MAC': 2,
            'NCS': 2,
            'RW': {
                'TI': 40,
                'TT': 0.3
            },
            'NCM': 4
        }
    }
    
    headers = {'Content-Type': 'application/json'}
    
    try:
        response = requests.post(url, json=data, headers=headers)
        print(f'Status Code: {response.status_code}')
        print(f'Response: {response.text}')
    except Exception as e:
        print(f'Error: {str(e)}')

if __name__ == '__main__':
    test_rwscan()
