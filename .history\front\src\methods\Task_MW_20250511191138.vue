<template>
  <div class="analysis-container">
    <div class="analysis-header">
      <div class="header-nav fade-in">
        <a-button type="text" @click="returnUp" class="nav-button">
          <ArrowLeftOutlined />
          返回任务列表
        </a-button>
        <a-button type="text" @click="returnMain" class="nav-button">
          <HomeFilled />
          返回首页
        </a-button>
        <a-button type="text" @click="logOut" class="nav-button">
          <LogoutOutlined />
          退出登录
        </a-button>
      </div>
      <img src="../assets/hitsz.png" class="logo-image fade-in" alt="Logo">
    </div>
    <div class="analysis-content">
      <div class="inputs-column slide-in-left">
        <Inputs
          v-if="shouldRender"
          :setTs="setTs"
          :atTs="atTs"
          :setTsFunc="setTsFunc"
          class="inputs-component"
          @start-scan="handleStartScan"
        />
      </div>
      <div class="results-column slide-in-right">
        <Results
          v-if="shouldRender"
          :changeTs="changeTs"
          :setTsFunc="setTsFunc"
          class="results-component"
        />
      </div>
    </div>
    <!-- Loading overlays -->
    <div v-if="$store.state.uploading" class="overlay uploading-overlay">
      <div class="spinner"></div>
      <p class="overlay-text">正在上传视频，请稍候...</p>
    </div>

    <div v-if="$store.state.task_ready" class="overlay task-ready-overlay">
      <div class="spinner"></div>
      <p class="overlay-text">正在准备任务数据，请稍候...</p>
    </div>

    <!-- 如果 $store.state.task_record.task_status等于“running”，显示遮罩-->
    <div v-if="$store.state.task_record.task_status === 'running'" class="taskRun-overlay">
        <div class="taskRun-spinner"></div>
        <p class="overlay-text">任务运行中，请稍候...</p>
        <p class="overlay-subtext">分析完成后将自动显示结果</p>
    </div>
  </div>
</template>

<script>
import Inputs from './MW/Inputs.vue';
import Results from './MW/Results.vue';
import { HomeFilled, LogoutOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';

export default {
  name: 'MWTaskView',
  data() {
    return {
      shouldRender: true,
      // 视频操控
      scan_signal: false,
      setTs: false,
      atTs: 0.0
    };
  },
  components: {
    Inputs,
    Results,
    HomeFilled,
    LogoutOutlined,
    ArrowLeftOutlined,
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.shouldRender = true;
    });
  },
  methods: {
    setTsFunc(newSignal) {
      this.setTs = newSignal;
    },
    changeTs(ts) {
      this.atTs = Number(ts);
    },
    handleStartScan(videoFile) {
      // 处理开始扫描事件
      console.log('开始扫描分析', videoFile);
      console.log('当前任务配置:', this.$store.state.task_record.task_config);
      console.log('任务ID:', this.$store.state.task_record.id);
      console.log('任务模式:', this.$store.state.task_record.task_mode);
      console.log('任务状态:', this.$store.state.task_record.task_status);
      console.log('提示词:', this.$store.state.task_record.task_config.prompt);

      // 检查提示词是否为空
      if (!this.$store.state.task_record.task_config.prompt || this.$store.state.task_record.task_config.prompt.trim() === '') {
        this.$message.error('请输入检测提示词');
        return;
      }

      // 检查视频是否已上传
      if (!this.$store.state.task_record.task_config.video_name) {
        this.$message.error('请先上传视频文件');
        return;
      }

      // 显示加载中消息
      this.$message.loading('正在启动分析任务...', 2);

      // 检查任务模式
      if (this.$store.state.task_record.task_mode === 'balanced') {
        // 显示请求详情
        console.log('准备发送请求到 /RWScan，请求数据:', {
          task_id: this.$store.state.task_record.id,
          task_config: this.$store.state.task_record.task_config
        });

        // 显示axios配置
        console.log('axios配置:', {
          baseURL: this.$axios.defaults.baseURL,
          headers: this.$axios.defaults.headers
        });

        // 调用后端API启动分析任务
        this.$axios.post('/RWScan', {
          task_id: this.$store.state.task_record.id,
          task_config: this.$store.state.task_record.task_config
        }, {
          timeout: 10000, // 设置超时时间为10秒
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          console.log('分析任务已启动，服务器响应:', res);
          console.log('响应数据:', res.data);

          // 更新任务状态为运行中
          let updatedTask = {...this.$store.state.task_record, task_status: 'running'};
          this.$store.dispatch('updateTaskRecord', updatedTask);
          console.log('任务状态已更新为running');

          // 显示成功消息
          this.$message.success('分析任务已启动，请稍等片刻查看结果', 3);

          // 不再跳转到任务列表页面，而是留在当前页面
          console.log('任务已启动，等待结果...');

          // 设置一个定时器，每5秒检查一次任务状态
          const checkTaskStatus = setInterval(() => {
            this.$axios.post('/taskDetail', {
              task_id: this.$store.state.task_record.id
            })
            .then(response => {
              const task = response.data.task;
              console.log('任务状态检查:', task.task_status);

              // 更新任务记录
              this.$store.dispatch('updateTaskRecord', task);

              // 如果任务已完成，清除定时器并显示成功消息
              if (task.task_status === 'finished') {
                clearInterval(checkTaskStatus);
                this.$message.success('分析任务已完成，正在加载结果', 3);
              }
            })
            .catch(error => {
              console.error('检查任务状态失败:', error);
            });
          }, 5000);
        })
        .catch((err) => {
          console.error('分析失败，错误详情:', err);

          if (err.response) {
            console.error('错误响应状态码:', err.response.status);
            console.error('错误响应数据:', err.response.data);

            // 显示详细错误信息
            if (err.response.data && err.response.data.error) {
              this.$message.error(`启动分析任务失败: ${err.response.data.error}`);
            } else {
              this.$message.error(`启动分析任务失败，状态码: ${err.response.status}`);
            }
          } else if (err.request) {
            console.error('请求已发送但未收到响应');
            this.$message.error('启动分析任务失败: 服务器未响应');
          } else {
            console.error('请求配置错误:', err.message);
            this.$message.error(`启动分析任务失败: ${err.message}`);
          }
        });
      } else {
        this.$message.error('该模式尚在开发中');
      }
    },
    logOut() {
      this.shouldRender = false; // 设置 shouldRender 为 false，销毁组件
      this.$router.push({ path: '/' });
    },
    returnMain() {
      this.shouldRender = false;
      console.log(this.$store.state.task_record);
      this.$router.push({ path: '/main' });
    },
    returnUp() {
      this.shouldRender = false;
      this.$router.push({ path: '/task' });
    },
  },
};
</script>


  <style scoped>
.analysis-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(24, 144, 255, 0.2);
  z-index: 10000;
  position: relative;
}

.header-nav {
  display: flex;
  gap: 20px;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  color: #333;
  transition: all 0.3s;
  padding: 8px 16px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.nav-button:hover {
  transform: translateY(-2px);
  color: #1890ff;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);
}

.logo-image {
  height: 50px;
}

.analysis-content {
  display: flex;
  flex: 1;
  padding: 20px;
  gap: 20px;
  min-height: calc(100vh - 80px);
}

.inputs-column, .results-column {
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: auto; /* 改为auto允许内容滚动 */
  border: 1px solid rgba(24, 144, 255, 0.1);
  position: relative;
  display: flex;
  flex-direction: column;
}

.inputs-column::before, .results-column::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background-color: #1890ff;
  opacity: 0.8;
  z-index: 1;
}

.inputs-column {
  width: 40%;
  flex-direction: column;
  min-height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
}

.results-column {
  width: 60%;
  min-height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
}

.inputs-component, .results-component {
  height: 100%;
  overflow: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.spinner {
  width: 70px;
  height: 70px;
  border: 5px solid rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  border-top-color: #1890ff;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);
}

.overlay-text {
  color: #333;
  font-size: 22px;
  font-weight: 600;
  text-align: center;
  max-width: 80%;
  background: rgba(255, 255, 255, 0.8);
  padding: 15px 30px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.taskRun-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.taskRun-spinner {
  width: 70px;
  height: 70px;
  border: 5px solid rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  border-top-color: #1890ff;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 视频控制按钮隐藏 */
.video::-webkit-media-controls-fullscreen-button {
  display: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .analysis-content {
    flex-direction: column;
  }

  .inputs-column, .results-column {
    width: 100%;
  }

  .inputs-column {
    min-height: 50vh;
    margin-bottom: 20px;
  }

  .results-column {
    min-height: 50vh;
  }

  .logo-image {
    height: 40px;
  }
}
  </style>
