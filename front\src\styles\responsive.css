/* 小屏幕设备 (手机, 600px 及以下) */
@media only screen and (max-width: 600px) {
  .login-card {
    width: 90%;
    padding: 20px;
  }
  
  .feature-cards {
    flex-direction: column;
  }
  
  .feature-card {
    width: 100%;
  }
  
  .task-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-right {
    width: 100%;
    flex-direction: column;
  }
  
  .search-box {
    width: 100%;
  }
  
  .search-input {
    width: 100%;
  }
  
  .analysis-content {
    flex-direction: column;
  }

  .inputs-column, .results-column {
    width: 100%;
    margin-bottom: 20px;
  }

  .main-view-btns {
    flex-direction: column;
  }

  .user-view-container {
    flex-direction: column;
  }

  .buttons-container {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
    margin-bottom: 20px;
  }

  .current-component {
    width: 100%;
  }
}

/* 中等屏幕设备 (平板电脑, 768px 及以上) */
@media only screen and (min-width: 601px) and (max-width: 992px) {
  .login-card {
    width: 70%;
  }

  .feature-card {
    width: 45%;
  }

  .analysis-content {
    flex-direction: column;
  }

  .inputs-column, .results-column {
    width: 100%;
    margin-bottom: 20px;
  }

  .user-view-container {
    flex-direction: column;
  }

  .buttons-container {
    width: 100%;
    flex-direction: row;
    margin-bottom: 20px;
  }

  .current-component {
    width: 100%;
  }
}

/* 大屏幕设备 (笔记本/台式机, 992px 及以上) */
@media only screen and (min-width: 992px) {
  .login-card {
    width: 500px;
  }

  .feature-card {
    width: 30%;
  }

  .analysis-content {
    flex-direction: row;
  }

  .inputs-column, .results-column {
    width: 48%;
  }

  .user-view-container {
    flex-direction: row;
  }

  .buttons-container {
    width: 250px;
  }

  .current-component {
    flex: 1;
  }
}

/* 超大屏幕设备 (大型显示器, 1200px 及以上) */
@media only screen and (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }

  .feature-card {
    width: 23%;
  }
}

/* 打印样式 */
@media print {
  body {
    background-color: white;
  }

  .no-print {
    display: none;
  }

  .container {
    width: 100%;
    max-width: none;
    padding: 0;
    margin: 0;
  }
}
