from sqlalchemy import create_engine, inspect, text
import os

os.chdir(os.path.dirname(__file__))

# 创建一个引擎，连接到SQLite数据库文件
engine = create_engine('sqlite:///database.db', echo=True)

# 使用Inspector来获取数据库信息
inspector = inspect(engine)

# 获取所有表格的名称
table_names = inspector.get_table_names()

# 创建一个会话
connection = engine.connect()

# 遍历每个表格并打印表头和内容
for table_name in table_names:
    print(f"\nTable: {table_name}")
    print("=========================================")

    # 获取表格的列信息
    columns = inspector.get_columns(table_name)

    # 打印表头
    header = [column['name'] for column in columns]
    print(" | ".join(header))

    # 使用text()对象查询表格的所有内容
    query = connection.execute(text(f"SELECT * FROM {table_name}"))
    
    # 打印内容
    for row in query.fetchall():
        print(" | ".join(map(str, row)))

    print("=========================================")

# 关闭连接
connection.close()
