// main.js
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store'; // 引入store
import Antd from 'ant-design-vue';
import axios from 'axios';
// 不需要导入Ant Design CSS，因为我们使用了主题定制
import './styles/global.css'; // 导入全局样式
import './styles/animations.css'; // 导入动画样式
import './styles/responsive.css'; // 导入响应式样式

axios.defaults.baseURL = 'http://localhost:5000';
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.timeout = 10000; // 设置超时时间为10秒

// 添加请求拦截器
axios.interceptors.request.use(
  config => {
    console.log('发送请求:', config.url, config.data);
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
axios.interceptors.response.use(
  response => {
    console.log('收到响应:', response.status, response.data);
    return response;
  },
  error => {
    console.error('响应错误:', error);
    if (error.response) {
      console.error('错误响应:', error.response.status, error.response.data);
    } else if (error.request) {
      console.error('未收到响应:', error.request);
    } else {
      console.error('请求配置错误:', error.message);
    }
    return Promise.reject(error);
  }
);

const app = createApp(App);
app.use(router);
app.use(Antd); // 使用Ant Design Vue
app.use(store); // 使用store
app.config.globalProperties.$axios = axios;
app.mount('#app');
