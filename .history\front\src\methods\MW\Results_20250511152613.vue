<template>
  <div class="results-box">
    <div class="results-body">
      <div class="results-head">
        <p class="content-head">候选片段列表</p>
        <p class="content-fill">总数: {{ $store.state.task_record.task_config.NCM }}</p>
      </div>
      <div class="results-block" >
        <a-table :dataSource="dataSource" :columns="columns" :pagination="{showSizeChanger: false, defaultPageSize: 6 }" style:{background-color:}>
          <template v-slot:bodyCell="{ column, record}">
            <template v-if="column.dataIndex === 'O'">
                <a @click="fullShow(record.O)"><img :src="record.O"/></a>
            </template>
            <template v-else-if="column.dataIndex === 'TP'">
                <a @click="handleTs(record.TP)">{{ formatTime(record.TP) }}</a>
            </template>
            <template v-else-if="column.dataIndex === 'V'">
                <button type="primary" @click="openVideo(record.V)">查看片段</button>
            </template>
        </template>
        </a-table>
      </div>
    </div>
  </div>
  <!-- 遮罩播放视频 -->
  <div v-if="videoShow" class="video-overlay">
    <div class="video-container">
      <video :src="videoSource" controls autoplay></video>
      <button class="vclose" @click="closeVideo">Close</button>
    </div>
  </div>

  <!-- 遮罩放映图片 -->
  <div v-if="imgShow" class="img-overlay">
    <div class="img-container">
      <img class="aimg" :src="imgSource" />
      <button class="iclose" @click="closeShow">Close</button>
    </div>
  </div>
</template>

<script>
export default {
    name: 'ResultsView',
    props: {
      changeTs: Function,
      setTsFunc: Function
    },
    watch: {

    },
    // 每隔一秒调用一次this.handleScanSignal();
    mounted() {
      // this.timer = setInterval(this.handleScanSignal, 1000);
    },
    // 创建组件时调用
    created() {
      this.handleScanSignal();
    },
    methods:{
      fullShow(url) {
        this.imgShow = true;
        this.imgSource = url;
      },
      closeShow() {
        this.imgShow = false;
      },
      openVideo(url) {
        this.videoShow = true;
        this.videoSource = url;
      },
      closeVideo() {
        this.videoShow = false;
      },
      convertBase64ToBlob(base64Data, videoFormat) {
        // 移除base64数据前缀
        const base64WithoutPrefix = base64Data.replace(/^data:video\/\w+;base64,/, '');

        // 将base64字符串解码为二进制数据
        const decodedContent = atob(base64WithoutPrefix);

        // 创建一个Uint8Array以存储二进制数据
        const arrayBuffer = new ArrayBuffer(decodedContent.length);
        const uint8Array = new Uint8Array(arrayBuffer);

        // 将解码后的二进制数据填充到Uint8Array中
        for (let i = 0; i < decodedContent.length; i++) {
          uint8Array[i] = decodedContent.charCodeAt(i);
        }

        // 创建Blob对象
        const blob = new Blob([uint8Array], { type: `video/${videoFormat}` });

        return blob;
      },
      formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;

        return `${this.padZero(hours)}:${this.padZero(minutes)}:${this.padZero(remainingSeconds)}`;
      },
      padZero(value) {
        return value < 10 ? `0${value}` : value;
      },
      handleTs(TP){
        this.setTsFunc(true);
        this.changeTs(TP);
      },
      handleScanSignal() {
        this.$axios.post('/showResult', {
          task_id: this.$store.state.task_record.id,
        })
          .then(response => {
            // 返回的response.data数据格式为字典:时间戳:(相似度,图片base64数据,图片后缀,视频base64数据,视频后缀)
            // 现在根据相似度对整个response.data进行排序
            let processData = response.data;
            let processList = [];
            for (let key in processData) {
              processList.push([key, processData[key]]);
            }
            processList.sort(function(a, b) {
              return b[1][0] - a[1][0];
            });
            // 创造一个列表,每行为一个字典，将排序后的数据放入其中,格式为{SN: 编号从1开始, S: 相似度, O: 图片base64数据, TP: 时间戳, V: 视频格式}
            let dataList = [];
            var blob, blobUrl;
            for (let i = 0; i < processList.length; i++) {
              let data = {};
              data.SN = i + 1;
              data.S = parseFloat(processList[i][1][0]).toFixed(4);
              data.O = 'data:image/' + processList[i][1][2] + ';base64,' + processList[i][1][1];
              data.TP = parseFloat(processList[i][0]).toFixed(2);
              blob = this.convertBase64ToBlob(processList[i][1][3], processList[i][1][4]);
              blobUrl = URL.createObjectURL(blob);
              data.V = blobUrl;
              dataList.push(data);
            }
            this.dataSource = dataList;
          })
          .catch(error => {
            console.error('Error fetching config:', error);
          })
          .finally(() => {
            this.$store.dispatch('updateScanning', false);
          });
        }
    },

    data() {
      return {
        videoShow: false,
        videoSource: '',
        imgShow: false,
        imgSource: '',
        CN: null,
        columns: [
          { title: '序列号', dataIndex: 'SN',sorter: (a, b) => a.SN - b.SN},
          { title: '相似度', dataIndex: 'S' , sorter: (a, b) => a.S - b.S},
          { title: '时间戳', dataIndex: 'TP',sorter: (a, b) => a.TP - b.TP},
          { title: '预览图', dataIndex: 'O'},
          { title: '', dataIndex: 'V'}
        ],
        dataSource: [
          // {SN: '1', S: 0.9172, O: 'favicon.ico', TP: 28 },
        ],
      };
    },
  };
</script>

<style>
.results-box {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  height: 100%;
  padding: 20px 0;
  background-color: rgba(255, 255, 255, 0.5);
}

.results-body {
  width: 95%;
  height: 100%;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.results-head {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
  margin-bottom: 20px;
}

.results-block {
  margin-top: 20px;
}

.content-head {
  text-align: left;
  color: #1890ff;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.content-fill {
  color: #666;
  font-size: 14px;
  font-weight: 500;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 5px 10px;
  border-radius: 4px;
}

img {
  width: 80px;
  height: auto;
}


.video-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background: rgba(255, 255, 255, 0.8); */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}


.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #2C3D50;
  border: 3px solid #FFFEFC;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}


.video-container video {
  width: 60%;
  height: auto;
}
/* 去掉video全屏按钮 */
.video-container video::-webkit-media-controls-fullscreen-button {
  display: none;
}


.vclose {
  position: absolute;
  top: 15vh;
  right: 20px;
  background-color: #2C3D50;
  /* border: 3px solid #FFFEFC; */
  border-radius: 10%;
  color: white;
  font-size: 24px;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
}

.img-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background: rgba(255, 255, 255, 0.8); */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.img-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #2C3D50;
  border: 3px solid #FFFEFC;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.aimg{
  /* 将原本的图片放大到宽度为80%,高度自动调整 */
  width: 60%;
  height: auto;
}


.iclose {
  position: absolute;
  top: 15vh;
  right: 20px;
  background-color: #2C3D50;
  /* border: 3px solid #FFFEFC; */
  border-radius: 10%;
  color: white;
  font-size: 24px;
  font-weight: bold;
  padding: 10px 20px;
  cursor: pointer;
}
</style>
