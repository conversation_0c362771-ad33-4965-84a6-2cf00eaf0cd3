<template>
  <div class="analysis-container">
    <div class="analysis-header">
      <div class="header-nav fade-in">
        <a-button type="text" @click="returnUp" class="nav-button">
          <ArrowLeftOutlined />
          返回任务列表
        </a-button>
        <a-button type="text" @click="returnMain" class="nav-button">
          <HomeFilled />
          返回首页
        </a-button>
        <a-button type="text" @click="logOut" class="nav-button">
          <LogoutOutlined />
          退出登录
        </a-button>
      </div>
      <img src="../assets/hitsz.png" class="logo-image fade-in" alt="Logo">
    </div>
    <div class="analysis-content">
      <div class="inputs-column slide-in-left">
        <Inputs
          v-if="shouldRender"
          :setTs="setTs"
          :atTs="atTs"
          :setTsFunc="setTsFunc"
          class="inputs-component"
        />
      </div>
      <div class="results-column slide-in-right">
        <Results
          v-if="shouldRender"
          :changeTs="changeTs"
          :setTsFunc="setTsFunc"
          class="results-component"
        />
      </div>
    </div>
    <!-- Loading overlays -->
    <div v-if="$store.state.uploading" class="overlay uploading-overlay">
      <div class="spinner"></div>
      <p class="overlay-text">正在上传视频，请稍候...</p>
    </div>

    <div v-if="$store.state.task_ready" class="overlay task-ready-overlay">
      <div class="spinner"></div>
      <p class="overlay-text">正在准备任务数据，请稍候...</p>
    </div>

    <!-- 如果 $store.state.task_record.task_status等于“running”，显示遮罩-->
    <div v-if="$store.state.task_record.task_status === 'running'" class="taskRun-overlay">
        <div class="taskRun-spinner"></div>
        <p>任务运行中...</p>
    </div>
  </div>
</template>

<script>
import Inputs from './MW/Inputs.vue';
import Results from './MW/Results.vue';
import { HomeFilled, LogoutOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';

export default {
  name: 'MWTaskView',
  data() {
    return {
      shouldRender: true,
      // 视频操控
      scan_signal: false,
      setTs: false,
      atTs: 0.0
    };
  },
  components: {
    Inputs,
    Results,
    HomeFilled,
    LogoutOutlined,
    ArrowLeftOutlined,
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.shouldRender = true;
    });
  },
  methods: {
    setTsFunc(newSignal) {
      this.setTs = newSignal;
    },
    changeTs(ts) {
      this.atTs = Number(ts);
    },
    logOut() {
      this.shouldRender = false; // 设置 shouldRender 为 false，销毁组件
      this.$router.push({ path: '/' });
    },
    returnMain() {
      this.shouldRender = false;
      console.log(this.$store.state.task_record);
      this.$router.push({ path: '/main' });
    },
    returnUp() {
      this.shouldRender = false;
      this.$router.push({ path: '/task' });
    },
  },
};
</script>


  <style scoped>
.analysis-container {
  min-height: 100vh;
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background-color: #2C3D50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border-bottom: 3px solid #FFFEFC;
  z-index: 10000;
}

.header-nav {
  display: flex;
  gap: 15px;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #FFFEFC;
  transition: all 0.3s;
}

.nav-button:hover {
  transform: translateY(-2px);
  color: #1890ff;
}

.logo-image {
  height: 50px;
}

.analysis-content {
  display: flex;
  flex: 1;
  padding: 0;
  gap: 0;
}

.inputs-column, .results-column {
  background-color: #2C3D50;
  opacity: 0.96;
  overflow: hidden;
}

.inputs-column {
  width: 40%;
  display: flex;
  flex-direction: column;
}

.results-column {
  width: 60%;
}

.inputs-component, .results-component {
  height: 100%;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(44, 61, 80, 0.95);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 5px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #1890ff;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

.overlay-text {
  color: #FFFEFC;
  font-size: 20px;
  font-weight: 500;
  text-align: center;
  max-width: 80%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 视频控制按钮隐藏 */
.video::-webkit-media-controls-fullscreen-button {
  display: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .analysis-content {
    flex-direction: column;
  }

  .inputs-column, .results-column {
    width: 100%;
  }

  .inputs-column {
    height: 40vh;
  }

  .results-column {
    height: 60vh;
  }

  .logo-image {
    height: 40px;
  }
}
  </style>
