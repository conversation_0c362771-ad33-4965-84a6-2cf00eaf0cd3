<template>
    <div class="inputs-box">
        <div class="buttons">
            <a-button type="primary" @click="triggerFileInput" class="action-button">
                <span class="button-icon">1</span>
                上传视频
            </a-button>
            <DoubleRightOutlined style="color: #1890ff;"/>
            <a-button type="primary" @click="updateSettings" class="action-button">
                <span class="button-icon">2</span>
                上传参数
            </a-button>
            <DoubleRightOutlined style="color: #1890ff;"/>
            <a-button type="primary" @click="scan" class="action-button">
                <span class="button-icon">3</span>
                生成分析
            </a-button>
        </div>

        <div class="settings">
            <div class="settings-container">
                <div class="settings-header">
                    <SettingFilled style="color: #1890ff; font-size: 22px;"/>
                    <h2>参数设定</h2>
                    <div class="preset-selector">
                        <a-select
                            placeholder="选择预设配置"
                            style="width: 150px;"
                            @change="applyPreset"
                        >
                            <a-select-option value="balanced">平衡模式</a-select-option>
                            <a-select-option value="speed">速度优先</a-select-option>
                            <a-select-option value="accuracy">精度优先</a-select-option>
                        </a-select>
                    </div>
                </div>

                <a-tabs default-active-key="1" class="settings-tabs">
                    <a-tab-pane key="1" tab="提示词设置">
                        <div class="prompt-section performance-params">
                            <div class="prompt-header">
                                <h3>检测提示词</h3>
                                <p class="prompt-description">输入您想要在视频中检测的目标或场景的描述</p>
                            </div>
                            <div class="prompt-input">
                                <a-textarea
                                    placeholder="例如：'城市街道上的垃圾'、'人行道上的积水'、'损坏的公共设施'等"
                                    :rows="4"
                                    v-model="$store.state.task_record.task_config.prompt"
                                    allow-clear
                                />
                            </div>
                            <div class="prompt-tips">
                                <a-alert
                                    message="提示"
                                    description="详细、具体的描述会提高检测准确率。可以包含目标的颜色、形状、位置等特征。"
                                    type="info"
                                    show-icon
                                />
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="2" tab="性能参数">
                        <div class="params-grid performance-params">
                            <div class="param-card">
                                <div class="param-icon">
                                    <ThunderboltOutlined />
                                </div>
                                <div class="param-content">
                                    <h3>最大并发数量</h3>
                                    <a-input-number
                                        v-model="$store.state.task_record.task_config.MAC"
                                        :min="1"
                                        :max="8"
                                        class="param-input"
                                    />
                                    <div class="param-slider">
                                        <a-slider
                                            v-model="$store.state.task_record.task_config.MAC"
                                            :min="1"
                                            :max="8"
                                            :step="1"
                                        />
                                    </div>
                                    <p class="param-description">设置最大可用的并行处理数量，数值越大处理速度越快</p>
                                </div>
                            </div>

                            <div class="param-card">
                                <div class="param-icon">
                                    <SplitCellsOutlined />
                                </div>
                                <div class="param-content">
                                    <h3>分段数量</h3>
                                    <a-input-number
                                        v-model="$store.state.task_record.task_config.NCS"
                                        :min="1"
                                        :max="10"
                                        class="param-input"
                                    />
                                    <div class="param-slider">
                                        <a-slider
                                            v-model="$store.state.task_record.task_config.NCS"
                                            :min="1"
                                            :max="10"
                                            :step="1"
                                        />
                                    </div>
                                    <p class="param-description">设置视频分段处理的数量，适当分段可提高处理效率</p>
                                </div>
                            </div>

                            <div class="param-card">
                                <div class="param-icon">
                                    <AppstoreOutlined />
                                </div>
                                <div class="param-content">
                                    <h3>候选片段数量</h3>
                                    <a-input-number
                                        v-model="$store.state.task_record.task_config.NCM"
                                        :min="1"
                                        :max="10"
                                        class="param-input"
                                    />
                                    <div class="param-slider">
                                        <a-slider
                                            v-model="$store.state.task_record.task_config.NCM"
                                            :min="1"
                                            :max="10"
                                            :step="1"
                                        />
                                    </div>
                                    <p class="param-description">设定期望生成的结果片段数量，数值越大结果越详细</p>
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="3" tab="高级设置">
                        <div class="params-grid performance-params">
                            <div class="param-card">
                                <div class="param-icon">
                                    <PercentageOutlined />
                                </div>
                                <div class="param-content">
                                    <h3>相似度终止阈值</h3>
                                    <a-input-number
                                        v-model="$store.state.task_record.task_config.RW.TT"
                                        :min="0.1"
                                        :max="0.9"
                                        :step="0.05"
                                        :precision="2"
                                        class="param-input"
                                    />
                                    <div class="param-slider">
                                        <a-slider
                                            v-model="$store.state.task_record.task_config.RW.TT"
                                            :min="0.1"
                                            :max="0.9"
                                            :step="0.05"
                                            :tipFormatter="value => `${(value * 100).toFixed(0)}%`"
                                        />
                                    </div>
                                    <p class="param-description">设定终止的最小相似度阈值，值越高要求越严格</p>
                                </div>
                            </div>

                            <div class="param-card">
                                <div class="param-icon">
                                    <FieldTimeOutlined />
                                </div>
                                <div class="param-content">
                                    <h3>终止时间间隔</h3>
                                    <a-input-number
                                        v-model="$store.state.task_record.task_config.RW.TI"
                                        :min="10"
                                        :max="100"
                                        :step="5"
                                        class="param-input"
                                        suffix="秒"
                                    />
                                    <div class="param-slider">
                                        <a-slider
                                            v-model="$store.state.task_record.task_config.RW.TI"
                                            :min="10"
                                            :max="100"
                                            :step="5"
                                        />
                                    </div>
                                    <p class="param-description">设定终止的最小时间间隔，值越小结果粒度越细</p>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-tips">
                            <a-alert
                                message="高级参数说明"
                                description="这些参数会直接影响检测的精度和速度。如果您不确定如何设置，建议使用预设配置。"
                                type="warning"
                                show-icon
                            />
                        </div>
                    </a-tab-pane>
                </a-tabs>

                <div class="settings-actions">
                    <a-button type="primary" @click="updateSettings">
                        <SaveOutlined />
                        保存参数设置
                    </a-button>
                    <a-button @click="resetSettings">
                        重置
                    </a-button>
                </div>
            </div>
        </div>

        <input type="file" ref="fileInput" style="display: none" @change="handleFileChange">
    </div>
</template>

<script>
import { SettingFilled, SaveOutlined, ThunderboltOutlined, SplitCellsOutlined, AppstoreOutlined, PercentageOutlined, FieldTimeOutlined, DoubleRightOutlined } from '@ant-design/icons-vue';
import eventBus from '@/utils/eventBus';

export default {
    name: 'InputsComponent',
    components: {
        SettingFilled,
        SaveOutlined,
        ThunderboltOutlined,
        SplitCellsOutlined,
        AppstoreOutlined,
        PercentageOutlined,
        FieldTimeOutlined,
        DoubleRightOutlined
    },
    data() {
        return {
            videoFile: null,
        }
    },
    methods: {
        triggerFileInput() {
            this.$refs.fileInput.click();
        },
        handleFileChange(event) {
            const file = event.target.files[0];
            if (file) {
                this.videoFile = file;
                const fileURL = URL.createObjectURL(file);

                // 创建一个临时视频元素来获取视频信息
                const tempVideo = document.createElement('video');
                tempVideo.src = fileURL;

                // 通过事件总线发送视频加载事件
                eventBus.emit('video-loaded', tempVideo);

                // 更新任务记录中的文件名
                this.$store.commit('updateTaskFileName', file.name);

                // 显示成功消息
                this.$message.success('视频上传成功');
            }
        },
        updateSettings() {
            // 保存参数设置
            this.$message.success('参数设置已更新');
        },
        resetSettings() {
            // 重置参数为默认值
            this.$store.commit('resetTaskConfig');
            this.$message.info('参数已重置为默认值');
        },
        scan() {
            if (!this.videoFile) {
                this.$message.error('请先上传视频文件');
                return;
            }

            // 检查是否设置了提示词
            if (!this.$store.state.task_record.task_config.prompt) {
                this.$message.error('请输入检测提示词');
                return;
            }

            // 开始扫描分析
            this.$emit('start-scan', this.videoFile);
        },
        applyPreset(value) {
            // 应用预设配置
            switch(value) {
                case 'balanced':
                    // 平衡模式
                    this.$store.commit('updateTaskConfig', {
                        MAC: 4,
                        NCS: 5,
                        NCM: 5,
                        RW: { TT: 0.5, TI: 30 }
                    });
                    break;
                case 'speed':
                    // 速度优先
                    this.$store.commit('updateTaskConfig', {
                        MAC: 8,
                        NCS: 8,
                        NCM: 3,
                        RW: { TT: 0.3, TI: 45 }
                    });
                    break;
                case 'accuracy':
                    // 精度优先
                    this.$store.commit('updateTaskConfig', {
                        MAC: 2,
                        NCS: 3,
                        NCM: 8,
                        RW: { TT: 0.7, TI: 15 }
                    });
                    break;
            }
            this.$message.success(`已应用${value === 'balanced' ? '平衡模式' : value === 'speed' ? '速度优先' : '精度优先'}预设`);
        }
    }
}
</script>

<style scoped>
/* 整体容器 */
.inputs-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    background-color: transparent;
    padding: 10px;
    overflow: auto;
}

/* 按钮区域 */
.buttons {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    height: auto;
    width: 90%;
    background-color: rgba(255, 255, 255, 0.8);
    position: relative;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(24, 144, 255, 0.1);
    z-index: 10; /* 确保按钮在最上层 */
}

/* 操作按钮样式 */
.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding: 0 20px;
}

/* 按钮内的数字图标 */
.button-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: white;
    color: #1890ff;
    font-weight: bold;
    margin-right: 8px;
}



/* 设置区域 */
.settings {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-top: 0;
    margin-bottom: 20px;
    border: 1px solid rgba(24, 144, 255, 0.1);
    overflow-y: auto; /* 添加垂直滚动条 */
    max-height: 80vh; /* 增加最大高度 */
    z-index: 1; /* 确保元素在正确的层级 */
    position: relative; /* 添加相对定位 */
}

.settings-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

/* 设置头部 */
.settings-header {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(24, 144, 255, 0.1);
    margin-bottom: 20px;
}

.settings-header h2 {
    margin: 0 auto 0 10px;
    font-size: 20px;
    color: #1890ff;
}

/* 预设选择器 */
.preset-selector {
    margin-left: auto;
}

/* 提示词部分样式 */
.prompt-section {
    padding: 20px;
    background-color: white;
    border-radius: 0 0 8px 8px;
    width: 100% !important;
    min-width: 500px !important;
}

.prompt-header {
    margin-bottom: 15px;
}

.prompt-header h3 {
    font-size: 18px;
    margin-bottom: 5px;
    color: #333;
}

.prompt-description {
    color: #666;
    font-size: 14px;
}

.prompt-input {
    margin-bottom: 15px;
}

.prompt-tips {
    margin-top: 20px;
}

/* 标签页样式 */
.settings-tabs {
    width: 100%;
}

.ant-tabs-tabpane {
    z-index: 5;
    position: relative;
    min-width: 500px !important; /* 增加最小宽度 */
    width: 100% !important; /* 强制宽度一致 */
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

/* 参数网格布局 */
.params-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    width: 100%;
    padding: 20px;
    background-color: white;
    border-radius: 0 0 8px 8px;
}

/* 参数卡片 */
.param-card {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.param-icon {
    font-size: 24px;
    color: #1890ff;
    margin-right: 15px;
    padding-top: 5px;
}

.param-content {
    flex: 1;
}

.param-content h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: #333;
}

.param-input {
    margin-bottom: 10px;
}

.param-slider {
    margin-bottom: 10px;
}

.param-description {
    font-size: 12px;
    color: #666;
    margin: 0;
}

/* 高级设置提示 */
.advanced-tips {
    margin-top: 20px;
    width: 100%;
    padding: 0 20px;
}

/* 设置操作按钮 */
.settings-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    width: 100%;
}



/* 确保性能参数标签页内容不会变窄 */
.performance-params {
    width: 100% !important;
    min-width: 500px !important;
}

/* 确保所有标签页内容区域宽度一致 */
.ant-tabs-content {
    width: 100% !important;
    min-width: 500px !important;
}

/* 确保标签页容器宽度一致 */
.settings-tabs .ant-tabs {
    width: 100% !important;
    min-width: 500px !important;
}

/* 确保标签页内容宽度一致 */
.settings-tabs {
    width: 100% !important;
    min-width: 500px !important;
}

/* 确保参数网格布局宽度一致 */
.params-grid {
    width: 100% !important;
    min-width: 500px !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .params-grid {
        grid-template-columns: 1fr;
    }

    .settings-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .preset-selector {
        margin-top: 10px;
        align-self: flex-end;
    }

    /* 在小屏幕上调整样式 */
}
</style>
