from moviepy.editor import VideoFileClip
import os
from PIL import Image
import cv2
import torch 
# 多线程

# timestamp_list是一个二元组列表,第一个元素是时间戳,第二个元素是相似度
def expand(video_path, timestamp_list, output_directory,model,preprocess,text,STOPTHRESHOLD,STOPINTERVAL):
    # 读取视频
    video = VideoFileClip(video_path)
    # 遍历时间戳列表
    leftTs = []
    rightTs = []
    for ts, sim in timestamp_list:
        # 计算向左扩张的时间长度
        # 先从ts向左找到第一个相似度小于阈值的时间戳
        left_ts = ts
        while left_ts >= 0:
            # 读取帧
            try:
                frame = video.get_frame(left_ts)
            except:
                frame = None
            # 计算相似度
            simthis = calculateSim(frame,model,preprocess,text)
            # 如果相似度小于阈值,或者与ts的时间差大于STOPINTERVAL的一半,则停止向左扩张
            if simthis < STOPTHRESHOLD and ts - left_ts >= STOPINTERVAL / 6:
                break
            # 否则继续向左扩张一帧
            left_ts -= STOPINTERVAL/6
            if ts - left_ts >= STOPINTERVAL / 2:
                break
        left_ts = max(0,left_ts)
        leftTs.append(left_ts)
        # 计算向右扩张的时间长度
        # 先从ts向右找到第一个相似度小于阈值的时间戳
        right_ts = ts
        while right_ts < video.duration:
            # 读取帧
            try:
                frame = video.get_frame(right_ts)
            except:
                frame = None
            # 计算相似度
            simthis = calculateSim(frame,model,preprocess,text)
            # 如果相似度小于阈值,或者与ts的时间差大于STOPINTERVAL的一半,则停止向右扩张
            if simthis < STOPTHRESHOLD and right_ts - ts >= STOPINTERVAL / 6:
                break
            # 否则继续向右扩张一帧
            right_ts += STOPINTERVAL/6
            if right_ts - ts >= STOPINTERVAL / 2:
                break
        right_ts = min(int(video.duration),right_ts)
        rightTs.append(right_ts)

    # 用expandVideoAtTimestamps扩张视频
    expandVideoAtTimestamps(video, timestamp_list, output_directory, leftTs,rightTs)
    # 关闭视频
    video.reader.close()


def calculateSim(frame,model,preprocess,text):
    if frame is None:
        return 1
    image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    image = Image.fromarray(image)
    image = preprocess(image).unsqueeze(0)
    with torch.no_grad():
        logits_per_image, _ = model(image,text)
    logits_per_image=logits_per_image.detach().numpy()
    return round(logits_per_image[0][0]/100,4)


def expandVideoAtTimestamps(video, timestamp_list, output_directory, leftTs,rightTs):
    # 创建输出目录
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    for i in range(len(timestamp_list)):
        print(leftTs[i], rightTs[i])
        # 提取视频片段
        expanded_clip = video.subclip(leftTs[i], rightTs[i])

        # 构造保存文件名
        filename = f"{timestamp_list[i][0]}_{timestamp_list[i][1]}.mp4"

        # 保存视频片段
        expanded_clip.write_videofile(os.path.join(output_directory, filename), codec="libx264")