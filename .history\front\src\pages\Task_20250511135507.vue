<template>
  <div class="task-container">
    <div class="task-header">
      <div class="header-left fade-in">
        <a-button @click="returnMain" type="text" class="nav-button">
          <HomeFilled />
          返回首页
        </a-button>
        <h1 class="page-title">会话管理</h1>
      </div>
      <div class="header-right fade-in">
        <div class="search-box">
          <a-input
            placeholder="搜索会话描述..."
            v-model:value="search"
            class="search-input"
          >
            <template #prefix><SearchOutlined /></template>
          </a-input>
          <a-button type="primary" @click="searchTask" class="search-button">
            搜索
          </a-button>
        </div>
        <a-button type="primary" @click="getTasks" class="refresh-button">
          <ReloadOutlined />
          刷新
        </a-button>
        <a-button type="primary" @click="addTask" class="add-button">
          <PlusOutlined />
          新建会话
        </a-button>
        <a-button @click="logOut" type="text" class="logout-button">
          <LogoutOutlined />
        </a-button>
      </div>
    </div>

    <div class="task-content slide-in-up">
      <a-table
        :dataSource="dataSource"
        :columns="columns"
        :pagination="{
          showSizeChanger: false,
          defaultPageSize: 8,
          showTotal: total => `共 ${total} 条记录`
        }"
        class="task-table"
        :rowClassName="() => 'task-row'"
      >
        <template v-slot:bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'task_mode'">
            <a-tag v-if="record.task_mode === 'balanced'" color="blue" class="mode-tag">平衡模式</a-tag>
            <a-tag v-else-if="record.task_mode === 'efficient'" color="green" class="mode-tag">高效模式</a-tag>
            <a-tag v-else-if="record.task_mode === 'accurate'" color="red" class="mode-tag">精确模式</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'task_status'">
            <a-tag v-if="record.task_status === 'preparing'" color="blue" class="status-tag">待处理</a-tag>
            <a-tag v-else-if="record.task_status === 'running'" color="orange" class="status-tag">处理中</a-tag>
            <a-tag v-else-if="record.task_status === 'finished'" color="green" class="status-tag">已完成</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'check'">
            <a-button @click="checkThisTask(record)" type="primary" class="action-button view-button">
              <EyeOutlined />
              查看
            </a-button>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <a-button @click="showDeleteConfirm(record)" type="primary" danger class="action-button delete-button">
              <DeleteOutlined />
              删除
            </a-button>
          </template>
        </template>
      </a-table>
    </div>

    <a-modal
      v-model:open="openCreateTask"
      title="创建新会话"
      @ok="handleOk"
      class="create-task-modal"
      cancelText="取消"
      okText="创建"
      :maskClosable="false"
    >
      <div class="modal-content">
        <div class="form-item">
          <label>选择模式:</label>
          <a-select
            v-model:value="selectMode"
            class="mode-select"
          >
            <a-select-option value="balanced">平衡模式</a-select-option>
            <a-select-option value="efficient">高效模式</a-select-option>
            <a-select-option value="accurate">精确模式</a-select-option>
          </a-select>
        </div>
        <div class="form-item">
          <label>会话描述:</label>
          <a-textarea
            v-model:value="description"
            placeholder="请输入会话描述..."
            :auto-size="{ minRows: 3, maxRows: 6 }"
            class="description-textarea"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

  <script>
  import { PlusOutlined,HomeFilled,LogoutOutlined,ReloadOutlined } from '@ant-design/icons-vue';
  export default {
    name: 'TaskView',
    components: {
        PlusOutlined,
        HomeFilled,
        LogoutOutlined,
        ReloadOutlined
    },
    data() {
      return {
        columns: [
        //   { title: 'ID', dataIndex: 'id', key: 'id' },
          { title: '创建时间', dataIndex: 'create_date', key: 'create_date', sorter: (a, b) => a.create_date.localeCompare(b.create_date) },
          { title: '更新时间', dataIndex: 'update_date', key: 'update_date', sorter: (a, b) => a.update_date.localeCompare(b.update_date) },
          { title: '模式', dataIndex: 'task_mode', key: 'task_mode' ,sorter: (a, b) => a.task_mode.localeCompare(b.task_mode)},
          { title: '状态', dataIndex: 'task_status', key: 'task_status'  ,sorter: (a, b) => a.task_status.localeCompare(b.task_status)},
          { title: '会话描述', dataIndex: 'task_description', key: 'task_description' },
          { title: '', dataIndex: 'check', key: 'check' },
          { title: '', dataIndex: 'operation', key: 'operation' }
        ],
        dataSource: [],
        openCreateTask: false,
        selectMode: 'balanced',
        description: '',
        search: '',
      };
    },
    // 刚进入该路由时获取会话数据
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.getTasks();
      });
    },
    methods: {
      getTasks() {
        this.$message.info('正在获取会话数据...'); // Show a message to indicate the data is being fetched
        // Assuming this.$axios is configured correctly for your backend API
        this.$axios.post('/getTaskByUser', { username: this.$store.state.username }).then((res) => {
          if (res.data.code === 200) {
            console.log('获取会话数据成功:', res.data.tasks);
            this.dataSource = res.data.tasks; // Adjust based on your actual API response structure
            console.log('获取会话数据成功:', this.dataSource);
          } else {
            console.error('获取会话数据失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('获取会话数据失败:', err);
        });
      },
        addTask() {
            this.openCreateTask = true;

        },
        handleOk() {
            this.$axios.post('/createTask', { username: this.$store.state.username, task_mode: this.selectMode, task_description: this.description }).then((res) => {
                if (res.data.code === 200) {
                    this.$message.success('创建会话成功',res.data.task_id);
                    this.getTasks();
                } else {
                    this.$message.error('创建会话失败:' + res.data.message);
                }
            }).catch((err) => {
                this.$message.error('创建会话失败:' + err);
            });
            this.selectMode = 'balanced';
            this.description = '';
            this.openCreateTask = false;
        },
        returnMain() {
            this.$router.push('/main');
        },
        logOut() {
            this.$router.push('/');
        },
        checkThisTask(record) {
            this.$message.info('查看会话详情:' + record.id);
            this.$axios.post('updateTaskTime', { task_id: record.id }).then((res) => {
                if (res.data.code === 200) {
                    console.log('更新会话时间成功:', res.data.task);
                    if(record.task_mode === 'balanced'){
                        this.$store.dispatch('updateTaskRecord', res.data.task);
                        this.$router.push('/task/balanced');
                    }
                } else {
                    console.error('更新会话时间失败:', res.data.message);
                }
            }).catch((err) => {
                console.error('更新会话时间失败:', err);
            });
        },
        showDeleteConfirm(record) {
            this.$axios.post('/deleteTask', { task_id: record.id ,username:this.$store.state.username}).then((res) => {
                if (res.data.code === 200) {
                    this.$message.success('删除会话成功');
                    this.getTasks();
                } else {
                    this.$message.error('删除会话失败:' + res.data.message);
                }
            }).catch((err) => {
                this.$message.error('删除会话失败:' + err);
            });
        },
        searchTask() {
            this.$message.info('正在搜索会话...');
            if (this.search === '') {
                this.getTasks();
            } else {
                this.$axios.post('/getTaskByUser', { username: this.$store.state.username }).then((res) => {
                if (res.data.code === 200) {
                    let searchResult = res.data.tasks.filter((task) => {
                        return task.task_description.includes(this.search);
                    });
                    this.dataSource = searchResult; // Adjust based on your actual API response structure
                    console.log('获取会话数据成功:', this.dataSource);
                } else {
                    console.error('获取会话数据失败:', res.data.message);
                }
                }).catch((err) => {
                console.error('获取会话数据失败:', err);
                });


            }
        },
    },
  };
  </script>
  <style>
    .add-search-task {
        display: flex;
        justify-content: space-between;
        position: relative;
    }
    .log-out {
        position: absolute;
        left: 0;
        bottom: 30px;
    }
    .log-out > .ant-btn {
        font-weight: bold;
        margin-right: 20px;
    }
    .return {
        position: absolute;
        /* 水平居中 */
        left: 48%;
        bottom: 30px;
    }
    .return > .ant-btn {
    }
    .about-task {
        display: flex;
        flex-direction: row;
    }
    .search-task {
        display: flex;
        flex-direction: row;
        position: absolute;
        right: 60px;
        bottom: 30px;
    }
    .add-task > .ant-btn {
        position: absolute;
        right: 0;
        bottom: 25px;
        margin-left: 10px;
        border-radius: 50%;
        height: 40px;
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .create-window {
        display: flex;
        flex-direction: column;
        width: 30vw;
        align-items: left;
        justify-content: left;
        margin: 0 auto;
    }
    .create-window > .ant-space{
        margin: 20px;
    }
    .create-window > .ant-space:last-child{
        /* 内部组件顶部对齐 */
        align-items: flex-start;
    }
    </style>