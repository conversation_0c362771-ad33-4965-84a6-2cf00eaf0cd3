// main.js
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store'; // 引入store
import Antd from 'ant-design-vue';
import axios from 'axios';
import 'ant-design-vue/dist/antd.min.css'; // 使用Ant Design样式
import './styles/global.css'; // 导入全局样式
import './styles/animations.css'; // 导入动画样式
import './styles/responsive.css'; // 导入响应式样式

axios.defaults.baseURL = 'http://localhost:5000';

const app = createApp(App);
app.use(router);
app.use(Antd); // 使用Ant Design Vue
app.use(store); // 使用store
app.config.globalProperties.$axios = axios;
app.mount('#app');
