<template>
  <div>
    <a-space direction="vertical" size="large">
      <div class="input-container">
        <span>用户名:</span>
        <a-input :value="username" disabled />
      </div>
      <div class="input-container">
        <span>密码:</span>
        <a-input :value="password" disabled />
      </div>
      <div class="input-container">
        <span>邮箱:</span>
        <a-input :value="email" disabled />
      </div>
      <div class="input-container">
        <span>是否为管理员:</span>
        <a-input :value="isAdmin ? '是' : '否'" disabled />
      </div>
    </a-space>
  </div>
</template>

<script>
export default {
  name: 'UserInfo',
  data() {
    return {
      username: this.$store.state.username,
      password: this.$store.state.password,
      email: this.$store.state.email,
      isAdmin: this.$store.state.is_admin,
    };
  },
  watch: {
    // 监听this.$store.state.username是否发生变化
    '$store.state.username': {
      handler() {
        this.username = this.$store.state.username;
        this.password = this.$store.state.password;
        this.email = this.$store.state.email;
        this.isAdmin = this.$store.state.is_admin;
      },
      deep: true
    },
  },
};
</script>

<style scoped>
.input-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.input-container span {
  margin-right: 30px; /* 调整间距根据需要 */
  font-size: 20px;
  margin-bottom: 20px;
}

.input-container .ant-input {
  width: 400px; /* 调整宽度根据需要 */
  font-size:20px;
  margin-bottom: 20px;
}
</style>
