<template>
    <div class="inputs-box">
        <div class="video-play">
            <video class="video" id="videoPlayer" ref="videoPlayer" controls>
            </video>
            <input type="file" ref="fileInput" style="display: none" @change="handleFileChange">
        </div>
        <div class="inputs">
            <div class="buttons">
                <a-button type="primary" @click="triggerFileInput" class="action-button">
                  <span class="button-icon">1</span>
                  上传视频
                </a-button>
                <DoubleRightOutlined style="color: #1890ff;"/>
                <a-button type="primary" @click="updateSettings" class="action-button">
                  <span class="button-icon">2</span>
                  上传参数
                </a-button>
                <DoubleRightOutlined style="color: #1890ff;"/>
                <a-button type="primary" @click="scan" class="action-button">
                  <span class="button-icon">3</span>
                  生成分析
                </a-button>
            </div>
            <div class="settings">
                <div class="settings-container">
                    <div class="settings-header">
                        <SettingFilled style="color: #1890ff; font-size: 22px;"/>
                        <h2>参数设定</h2>
                        <div class="preset-selector">
                            <a-select
                                placeholder="选择预设配置"
                                style="width: 150px;"
                                @change="applyPreset"
                            >
                                <a-select-option value="balanced">平衡模式</a-select-option>
                                <a-select-option value="speed">速度优先</a-select-option>
                                <a-select-option value="accuracy">精度优先</a-select-option>
                            </a-select>
                        </div>
                    </div>

                    <a-tabs default-active-key="1" class="settings-tabs">
                        <a-tab-pane key="1" tab="提示词设置">
                            <div class="prompt-section performance-params">
                                <div class="prompt-header">
                                    <h3>检测提示词</h3>
                                    <p class="prompt-description">输入您想要在视频中检测的目标或场景的描述</p>
                                </div>
                                <div class="prompt-input">
                                    <a-textarea
                                        placeholder="例如：'城市街道上的垃圾'、'人行道上的积水'、'损坏的公共设施'等"
                                        :rows="4"
                                        v-model="$store.state.task_record.task_config.prompt"
                                        allow-clear
                                    />
                                </div>
                                <div class="prompt-tips">
                                    <a-alert
                                        message="提示"
                                        description="详细、具体的描述会提高检测准确率。可以包含目标的颜色、形状、位置等特征。"
                                        type="info"
                                        show-icon
                                    />
                                </div>
                            </div>
                        </a-tab-pane>

                        <a-tab-pane key="2" tab="性能参数">
                            <div class="params-grid performance-params">
                                <div class="param-card">
                                    <div class="param-icon">
                                        <ThunderboltOutlined />
                                    </div>
                                    <div class="param-content">
                                        <h3>最大并发数量</h3>
                                        <a-input-number
                                            v-model="$store.state.task_record.task_config.MAC"
                                            :min="1"
                                            :max="8"
                                            class="param-input"
                                        />
                                        <div class="param-slider">
                                            <a-slider
                                                v-model="$store.state.task_record.task_config.MAC"
                                                :min="1"
                                                :max="8"
                                                :step="1"
                                            />
                                        </div>
                                        <p class="param-description">设置最大可用的并行处理数量，数值越大处理速度越快</p>
                                    </div>
                                </div>

                                <div class="param-card">
                                    <div class="param-icon">
                                        <SplitCellsOutlined />
                                    </div>
                                    <div class="param-content">
                                        <h3>分段数量</h3>
                                        <a-input-number
                                            v-model="$store.state.task_record.task_config.NCS"
                                            :min="1"
                                            :max="10"
                                            class="param-input"
                                        />
                                        <div class="param-slider">
                                            <a-slider
                                                v-model="$store.state.task_record.task_config.NCS"
                                                :min="1"
                                                :max="10"
                                                :step="1"
                                            />
                                        </div>
                                        <p class="param-description">设置视频分段处理的数量，适当分段可提高处理效率</p>
                                    </div>
                                </div>

                                <div class="param-card">
                                    <div class="param-icon">
                                        <AppstoreOutlined />
                                    </div>
                                    <div class="param-content">
                                        <h3>候选片段数量</h3>
                                        <a-input-number
                                            v-model="$store.state.task_record.task_config.NCM"
                                            :min="1"
                                            :max="10"
                                            class="param-input"
                                        />
                                        <div class="param-slider">
                                            <a-slider
                                                v-model="$store.state.task_record.task_config.NCM"
                                                :min="1"
                                                :max="10"
                                                :step="1"
                                            />
                                        </div>
                                        <p class="param-description">设定期望生成的结果片段数量，数值越大结果越详细</p>
                                    </div>
                                </div>
                            </div>
                        </a-tab-pane>

                        <a-tab-pane key="3" tab="高级设置">
                            <div class="params-grid performance-params">
                                <div class="param-card">
                                    <div class="param-icon">
                                        <PercentageOutlined />
                                    </div>
                                    <div class="param-content">
                                        <h3>相似度终止阈值</h3>
                                        <a-input-number
                                            v-model="$store.state.task_record.task_config.RW.TT"
                                            :min="0.1"
                                            :max="0.9"
                                            :step="0.05"
                                            :precision="2"
                                            class="param-input"
                                        />
                                        <div class="param-slider">
                                            <a-slider
                                                v-model="$store.state.task_record.task_config.RW.TT"
                                                :min="0.1"
                                                :max="0.9"
                                                :step="0.05"
                                                :tipFormatter="value => `${(value * 100).toFixed(0)}%`"
                                            />
                                        </div>
                                        <p class="param-description">设定终止的最小相似度阈值，值越高要求越严格</p>
                                    </div>
                                </div>

                                <div class="param-card">
                                    <div class="param-icon">
                                        <FieldTimeOutlined />
                                    </div>
                                    <div class="param-content">
                                        <h3>终止时间间隔</h3>
                                        <a-input-number
                                            v-model="$store.state.task_record.task_config.RW.TI"
                                            :min="10"
                                            :max="100"
                                            :step="5"
                                            class="param-input"
                                            suffix="秒"
                                        />
                                        <div class="param-slider">
                                            <a-slider
                                                v-model="$store.state.task_record.task_config.RW.TI"
                                                :min="10"
                                                :max="100"
                                                :step="5"
                                            />
                                        </div>
                                        <p class="param-description">设定终止的最小时间间隔，值越小结果粒度越细</p>
                                    </div>
                                </div>
                            </div>

                            <div class="advanced-tips">
                                <a-alert
                                    message="高级参数说明"
                                    description="这些参数会直接影响检测的精度和速度。如果您不确定如何设置，建议使用预设配置。"
                                    type="warning"
                                    show-icon
                                />
                            </div>
                        </a-tab-pane>
                    </a-tabs>

                    <div class="settings-actions">
                        <a-button type="primary" @click="updateSettings">
                            <SaveOutlined />
                            保存参数设置
                        </a-button>
                        <a-button @click="resetSettings">
                            重置
                        </a-button>
                    </div>
                </div>
            </div>
            <!-- 添加底部空白区域，确保参数设定框与底边有足够的距离 -->
            <div class="bottom-spacer"></div>
        </div>
    </div>
</template>

<script>
import {
    DoubleRightOutlined,
    SettingFilled,
    ThunderboltOutlined,
    SplitCellsOutlined,
    AppstoreOutlined,
    PercentageOutlined,
    FieldTimeOutlined,
    SaveOutlined
} from '@ant-design/icons-vue';

export default {
    name: 'InputsView',
    components: {
        DoubleRightOutlined,
        SettingFilled,
        ThunderboltOutlined,
        SplitCellsOutlined,
        AppstoreOutlined,
        PercentageOutlined,
        FieldTimeOutlined,
        SaveOutlined
    },
    props: {
        setTs: Boolean,
        atTs: Number,
        setTsFunc: Function
    },
    data() {
        return {
        };
    },
    // 创建组件时调用
    created() {
        this.$store.dispatch('updateTaskReady', true);
        this.downloadVideo();
    },

    beforeRouteEnter() {
        console.log('beforeRouteEnter');

    },
    watch: {
        setTs: function() {
            this.handleSetTs();
        }
    },
    methods: {
        handleSetTs() {
            if (this.setTs) {
                this.$refs.videoPlayer.currentTime = this.atTs;
                this.setTsFunc(false);
            }
        },
        // 下载视频
        downloadVideo() {
            this.$axios.get('/DownloadVideo', { params: { task_id: this.$store.state.task_record.id } }).then((res) => {
                console.log('下载视频成功:', res);
                URL.revokeObjectURL(this.$refs.videoPlayer.src);
                this.$refs.videoPlayer.src = res.request.responseURL;
            }).catch((err) => {
                console.error('下载视频失败:', err);
            }).finally(() => {
                this.$store.dispatch('updateTaskReady', false);
            });

        },

        // 上传视频
        triggerFileInput() {
            // 触发文件输入框点击事件
            this.$refs.fileInput.click();
        },
        handleFileChange(event) {
            // 处理文件选择变化事件
            const file = event.target.files[0];
            if (file) {
                // 通过URL.createObjectURL创建视频源
                this.videoSource = URL.createObjectURL(file);
                // 更新视频播放器的源
                this.$refs.videoPlayer.src = this.videoSource;

                // 发送视频文件到服务器
                this.uploadVideo(file);
            }
        },
        convertBase64ToBlob(base64Data, videoFormat) {
            console.log('base64Data:', base64Data);
            // 移除base64数据前缀
            const base64WithoutPrefix = base64Data.replace(/^data:video\/\w+;base64,/, '');

            // 将base64字符串解码为二进制数据
            const decodedContent = atob(base64WithoutPrefix);

            // 创建一个Uint8Array以存储二进制数据
            const arrayBuffer = new ArrayBuffer(decodedContent.length);
            const uint8Array = new Uint8Array(arrayBuffer);

            // 将解码后的二进制数据填充到Uint8Array中
            for (let i = 0; i < decodedContent.length; i++) {
            uint8Array[i] = decodedContent.charCodeAt(i);
        }

        // 创建Blob对象
        const blob = new Blob([uint8Array], { type: `video/${videoFormat}` });

        return blob;
      },
        async uploadVideo(file) {
            this.uploading = true; // 设置上传状态为true
            this.$store.dispatch('updateUploading', true);

            const formData = new FormData();
            formData.append('video', file);
            formData.append('task_id', this.$store.state.task_record.id);
            // 发送 POST 请求到服务器的/InputVideo接口
            this.$axios.post('/InputVideo', formData,{
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            }).then(response => {
                this.$store.dispatch('updateTaskRecord', response.data.task);
                console.log(response.data);
            }).catch(error => {
                console.error('Error uploading video:', error);
            }).finally(() => {
                this.$store.dispatch('updateUploading', false);
                 // 无论上传成功或失败，都将上传状态设置为false
            });

        },
        // 应用预设配置
        applyPreset(value) {
            let presetConfig = {};

            switch(value) {
                case 'balanced':
                    presetConfig = {
                        MAC: 4,
                        NCS: 4,
                        NCM: 4,
                        RW: {
                            TI: 40,
                            TT: 0.3
                        },
                        prompt: this.$store.state.task_record.task_config.prompt // 保留当前提示词
                    };
                    break;
                case 'speed':
                    presetConfig = {
                        MAC: 8,
                        NCS: 8,
                        NCM: 2,
                        RW: {
                            TI: 60,
                            TT: 0.2
                        },
                        prompt: this.$store.state.task_record.task_config.prompt
                    };
                    break;
                case 'accuracy':
                    presetConfig = {
                        MAC: 2,
                        NCS: 2,
                        NCM: 6,
                        RW: {
                            TI: 20,
                            TT: 0.5
                        },
                        prompt: this.$store.state.task_record.task_config.prompt
                    };
                    break;
            }

            // 更新配置
            this.$store.state.task_record.task_config = presetConfig;

            // 显示成功消息
            this.$message.success(`已应用${value === 'balanced' ? '平衡模式' : value === 'speed' ? '速度优先' : '精度优先'}预设配置`);
        },

        // 重置设置
        resetSettings() {
            // 确认对话框
            this.$confirm({
                title: '确定要重置所有参数吗？',
                content: '这将把所有参数恢复为默认值',
                okText: '确定',
                cancelText: '取消',
                onOk: () => {
                    // 重置为默认配置
                    const defaultConfig = {
                        MAC: 2,
                        NCS: 2,
                        NCM: 4,
                        RW: {
                            TI: 40,
                            TT: 0.3
                        },
                        prompt: '',
                        video_name: this.$store.state.task_record.task_config.video_name // 保留视频名称
                    };

                    // 更新配置
                    this.$store.state.task_record.task_config = defaultConfig;

                    // 显示成功消息
                    this.$message.success('已重置所有参数为默认值');
                }
            });
        },

        // 上传配置
        updateSettings() {
            // 显示加载中
            const loadingMessage = this.$message.loading('正在保存参数设置...', 0);

            this.$axios.post('InputSettings', {
                task_id: this.$store.state.task_record.id,
                task_config: this.$store.state.task_record.task_config
            }).then((res) => {
                if (res.data.code === 200) {
                    console.log('参数设置保存成功:', res.data.task);
                    this.$store.dispatch('updateTaskRecord', res.data.task);

                    // 关闭加载提示并显示成功消息
                    loadingMessage();
                    this.$message.success('参数设置已保存');
                } else {
                    console.error('参数设置保存失败:', res.data.message);

                    // 关闭加载提示并显示错误消息
                    loadingMessage();
                    this.$message.error('参数设置保存失败: ' + res.data.message);
                }
            }).catch((err) => {
                console.error('参数设置保存失败:', err);

                // 关闭加载提示并显示错误消息
                loadingMessage();
                this.$message.error('参数设置保存失败，请重试');
            });
        },
        scan() {
            if(this.$store.state.task_record.task_mode=='balanced'){
                // 显示加载中消息
                this.$message.loading('正在启动分析任务...', 2);

                console.log('开始调用RWScan接口，任务ID:', this.$store.state.task_record.id);
                console.log('当前任务配置:', this.$store.state.task_record.task_config);

                this.$axios.post('/RWScan', { task_id: this.$store.state.task_record.id})
                    .then((res) => {
                        console.log('分析任务已启动，服务器响应:', res);

                        // 更新任务状态为运行中
                        let updatedTask = {...this.$store.state.task_record, task_status: 'running'};
                        this.$store.dispatch('updateTaskRecord', updatedTask);
                        console.log('任务状态已更新为running');

                        // 显示成功消息
                        this.$message.success('分析任务已启动，请稍后查看结果', 3);

                        // 延迟2秒后跳转，给用户时间看到消息
                        setTimeout(() => {
                            console.log('准备跳转到任务列表页面');
                            this.shouldRender = false;
                            this.$router.push({ path: '/task' });
                        }, 2000);
                    })
                    .catch((err) => {
                        console.error('分析失败，错误详情:', err);
                        console.error('错误响应:', err.response);
                        this.$message.error('启动分析任务失败，请重试');
                    });
            }else{
                this.$message.error('模式开发中');
            }
        },
    },
};
</script>

<style scoped>
.inputs-box {
  width: 100%;
  min-height: 120vh; /* 增加最小高度，确保有更多空间 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.5);
  padding-bottom: 150px; /* 大幅增加底部内边距，确保有足够空间 */
}

.video-play {
  height: 40vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid rgba(24, 144, 255, 0.2);
  padding: 20px 0;
  background-color: rgba(255, 255, 255, 0.5);
}

#videoPlayer {
  height: 30vh;
  width: 90%;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background-color: #000;
}

.inputs {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  min-height: 60vh; /* 增加最小高度，确保有足够空间 */
  background-color: rgba(255, 255, 255, 0.5);
  padding: 20px 0 100px 0; /* 大幅增加底部内边距 */
  overflow-y: auto;
  margin-bottom: 50px; /* 增加底部边距，避免抵住底边 */
}

.buttons {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  height: auto;
  width: 90%;
  background-color: rgba(255, 255, 255, 0.8);
  position: relative;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(24, 144, 255, 0.1);
}
.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 20px;
  height: 40px;
  border-radius: 8px;
  transition: all 0.3s;
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
  font-weight: 500;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.2);
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  font-weight: bold;
}
.settings {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 90%;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-top: -50px; /* 向上移动整个框 */
  margin-bottom: 120px; /* 大幅增加底部边距，确保远离底边 */
  border: 1px solid rgba(24, 144, 255, 0.1);
  overflow: visible; /* 修改为visible，确保内容可见且可交互 */
  z-index: 1; /* 确保元素在正确的层级 */
  position: relative; /* 添加相对定位 */
  top: -80px; /* 向上移动 */
}

.settings-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 15px;
}

.settings-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 10px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(24, 144, 255, 0.2);
}

.settings-header h2 {
  margin: 0 0 0 10px;
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
}

.settings-tabs {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 提示词部分样式 */
.prompt-section {
  padding: 20px;
  background-color: white;
  border-radius: 0 0 8px 8px;
}

.prompt-header {
  margin-bottom: 15px;
}

.prompt-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 5px;
}

.prompt-description {
  color: #666;
  font-size: 14px;
}

.prompt-input {
  margin-bottom: 15px;
  position: relative;
  z-index: 10; /* 确保文本区域在最上层 */
}

/* 确保文本区域可交互 */
.prompt-input .ant-input,
.prompt-input .ant-input-affix-wrapper,
.prompt-input .ant-input-textarea,
.prompt-input textarea {
  pointer-events: auto !important;
  z-index: 100 !important; /* 确保在最上层 */
}

/* 修复Ant Design组件的交互问题 */
.ant-tabs-content {
  z-index: 5;
  position: relative;
}

.ant-tabs-tabpane {
  z-index: 5;
  position: relative;
  min-width: 300px !important; /* 设置最小宽度 */
  width: 100% !important; /* 强制宽度一致 */
}

.prompt-tips {
  margin-top: 20px;
}

/* 参数卡片网格布局 */
.params-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.param-card {
  display: flex;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.param-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.1);
}

.param-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 8px;
  margin-right: 15px;
  color: #1890ff;
  font-size: 20px;
}

.param-content {
  flex: 1;
}

.param-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.param-input {
  width: 100%;
  margin-bottom: 10px;
}

.param-slider {
  margin-bottom: 15px;
}

.param-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* 高级提示 */
.advanced-tips {
  padding: 0 20px 20px;
}

/* 操作按钮 */
.settings-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(24, 144, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 预设选择器 */
.preset-selector {
  display: flex;
  align-items: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .params-grid {
    grid-template-columns: 1fr;
  }

  .settings-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .preset-selector {
    margin-top: 10px;
    align-self: flex-end;
  }
}

textarea::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.MAC, .NCS {
  border-top: 3px solid #1890ff;
}

.DSM, .DSD {
  border-top: 3px solid #52c41a;
}

.NA, .TT, .TI {
  border-top: 3px solid #fa8c16;
}

.NCM {
  border-top: 3px solid #722ed1;
}

/* 底部空白区域 */
.bottom-spacer {
  width: 100%;
  height: 150px; /* 高度足够大，确保有足够的空白 */
  margin-top: 50px;
}

/* 确保性能参数标签页内容不会变窄 */
.performance-params {
  width: 100% !important;
  min-width: 300px !important;
}

/* 确保所有标签页内容区域宽度一致 */
.ant-tabs-content {
  width: 100% !important;
}

/* 确保标签页容器宽度一致 */
.settings-tabs .ant-tabs {
  width: 100% !important;
}

</style>
