from flask import Flask, jsonify
from flask_cors import CORS
from celery import Celery
import time

app = Flask(__name__)
CORS(app)
# 配置 Celery
celery = Celery(
    app.name,
    backend='redis://127.0.0.1:6379/0',
    broker='redis://127.0.0.1:6379/0'
)

# 定义后台任务
@celery.task
def background_task():
    print("Background task started.")
    # 模拟耗时操作
    time.sleep(600)
    print("Background task completed.")
    return "I'm back"

# 定义路由，接收请求时触发后台任务
@app.route('/scan', methods=['GET'])
def scan():
    # 异步调用后台任务
    result = background_task.delay()
    print(result.id)
    return jsonify({"task_id": result.id, "status": "Task started. Check status with /status/<task_id>"})

# 定义路由，检查后台任务状态
@app.route('/status/<task_id>', methods=['GET'])
def task_status(task_id):
    task = background_task.AsyncResult(task_id)
    if task.state == 'PENDING':
        response = {"status": "Task not yet started"}
    elif task.state == 'SUCCESS':
        response = {"status": "Task completed", "result": task.result}
    elif task.state == 'FAILURE':
        response = {"status": "Task failed", "error": str(task.result)}
    else:
        response = {"status": task.state}

    return jsonify(response)

if __name__ == '__main__':
    app.run(debug=True)
