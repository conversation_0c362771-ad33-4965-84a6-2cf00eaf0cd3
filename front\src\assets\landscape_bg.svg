<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 天空渐变 -->
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#e6f7ff" />
      <stop offset="100%" stop-color="#f0f7ff" />
    </linearGradient>
    
    <!-- 山脉渐变 -->
    <linearGradient id="mountainGradient1" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#d6e4ff" />
      <stop offset="100%" stop-color="#bbd6ff" />
    </linearGradient>
    
    <linearGradient id="mountainGradient2" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#c2d8ff" />
      <stop offset="100%" stop-color="#a3c9ff" />
    </linearGradient>
    
    <linearGradient id="mountainGradient3" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#aeceff" />
      <stop offset="100%" stop-color="#8ebcff" />
    </linearGradient>
    
    <!-- 云朵渐变 -->
    <radialGradient id="cloudGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.9" />
      <stop offset="100%" stop-color="#f0f7ff" stop-opacity="0.6" />
    </radialGradient>
  </defs>
  
  <!-- 背景天空 -->
  <rect width="1920" height="1080" fill="url(#skyGradient)" />
  
  <!-- 远山 -->
  <path d="M0,700 Q480,500 960,650 Q1440,500 1920,700 L1920,1080 L0,1080 Z" fill="url(#mountainGradient1)" />
  
  <!-- 中山 -->
  <path d="M0,800 Q480,600 960,750 Q1440,600 1920,800 L1920,1080 L0,1080 Z" fill="url(#mountainGradient2)" />
  
  <!-- 近山 -->
  <path d="M0,900 Q480,750 960,850 Q1440,750 1920,900 L1920,1080 L0,1080 Z" fill="url(#mountainGradient3)" />
  
  <!-- 云朵 -->
  <ellipse cx="300" cy="300" rx="100" ry="60" fill="url(#cloudGradient)" />
  <ellipse cx="400" cy="280" rx="120" ry="70" fill="url(#cloudGradient)" />
  <ellipse cx="500" cy="320" rx="80" ry="50" fill="url(#cloudGradient)" />
  
  <ellipse cx="1200" cy="250" rx="120" ry="70" fill="url(#cloudGradient)" />
  <ellipse cx="1300" cy="230" rx="100" ry="60" fill="url(#cloudGradient)" />
  <ellipse cx="1400" cy="270" rx="80" ry="50" fill="url(#cloudGradient)" />
  
  <ellipse cx="700" cy="180" rx="90" ry="55" fill="url(#cloudGradient)" />
  <ellipse cx="800" cy="160" rx="110" ry="65" fill="url(#cloudGradient)" />
  <ellipse cx="900" cy="200" rx="70" ry="45" fill="url(#cloudGradient)" />
</svg>
