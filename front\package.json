{"name": "front", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "electron:build": "vue-cli-service electron:build", "electron:serve": "vue-cli-service electron:serve", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps"}, "main": "background.js", "dependencies": {"@ant-design/icons": "^5.3.0", "@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.0.0-rc.6", "axios": "^1.6.7", "core-js": "^3.8.3", "mitt": "^3.0.1", "vue": "^3.2.13", "vue-router": "^4.2.5", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "electron": "^13.0.0", "electron-devtools-installer": "^3.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-cli-plugin-electron-builder": "~2.1.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}