// store/index.js
import Vuex from 'vuex';


export default new Vuex.Store({
  state: {
    username: '',
    password: '',
    email: '',
    is_admin: false,
    task_record:{},
    uploading: false,
    scanning: false,

    task_ready: false,
  },
  mutations: {
    updateUsername(state, newData) {
      state.username = newData;
    },
    updatePassword(state, newData) {
      state.password = newData;
    },
    updateEmail(state, newData) {
      state.email = newData;
    },
    updateIsAdmin(state, newData) {
      state.is_admin = newData;
    },
    updateTaskRecord(state, newData) {
      // 字典赋值
      state.task_record = Object.assign({}, state.task_record, newData);
      console.log(state.task_record);
    },
    updateUploading(state, newData) {
      state.uploading = newData;
    },
    updateScanning(state, newData) {
      state.scanning = newData;
    },
    updateTaskReady(state, newData) {
      state.task_ready = newData;
    },
    updateTaskConfig(state, newData) {
      // 更新任务配置
      if (!state.task_record.task_config) {
        state.task_record.task_config = {};
      }
      state.task_record.task_config = Object.assign({}, state.task_record.task_config, newData);
      console.log('Updated task config:', state.task_record.task_config);
    },
    updateTaskFileName(state, fileName) {
      // 更新任务文件名
      if (!state.task_record.task_config) {
        state.task_record.task_config = {};
      }
      state.task_record.task_config.video_name = fileName;
      console.log('Updated video name:', fileName);
    },
    resetTaskConfig(state) {
      // 重置任务配置为默认值
      const defaultConfig = {
        MAC: 2,
        NCS: 2,
        NCM: 4,
        RW: {
          TI: 40,
          TT: 0.3
        },
        prompt: '',
        video_name: state.task_record.task_config ? state.task_record.task_config.video_name : ''
      };

      if (!state.task_record.task_config) {
        state.task_record.task_config = {};
      }

      state.task_record.task_config = defaultConfig;
      console.log('Reset task config to default:', state.task_record.task_config);
    }
  },
  actions: {
    updateUsername(context, newData) {
      context.commit('updateUsername', newData);
    },
    updatePassword(context, newData) {
      context.commit('updatePassword', newData);
    },
    updateEmail(context, newData) {
      context.commit('updateEmail', newData);
    },
    updateIsAdmin(context, newData) {
      context.commit('updateIsAdmin', newData);
    },
    updateTaskRecord(context, newData) {
      context.commit('updateTaskRecord', newData);
    },
    updateUploading(context, newData) {
      context.commit('updateUploading', newData);
    },
    updateScanning(context, newData) {
      context.commit('updateScanning', newData);
    },
    updateTaskReady(context, newData) {
      context.commit('updateTaskReady', newData);
    },
    updateTaskConfig(context, newData) {
      context.commit('updateTaskConfig', newData);
    },
    updateTaskFileName(context, fileName) {
      context.commit('updateTaskFileName', fileName);
    },
    resetTaskConfig(context) {
      context.commit('resetTaskConfig');
    }
  }
});
