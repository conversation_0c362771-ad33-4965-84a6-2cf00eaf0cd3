import sqlite3
import json

# 连接到数据库
conn = sqlite3.connect('DB/database.db')
cursor = conn.cursor()

# 查询所有任务
print("所有任务:")
cursor.execute("SELECT * FROM tasks")
tasks = cursor.fetchall()
for task in tasks:
    print(f"ID: {task[0]}, 用户名: {task[1]}, 任务号: {task[2]}, 更新时间: {task[3]}, 创建时间: {task[4]}, 模式: {task[5]}, 状态: {task[6]}, 描述: {task[8]}")
    print(f"配置: {task[7]}")
    print("-" * 80)

# 关闭连接
conn.close()
