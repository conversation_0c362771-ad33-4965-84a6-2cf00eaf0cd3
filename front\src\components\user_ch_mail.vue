<template>
  <div class="user-change-email-container">
    <!-- 验证码输入行 -->
    <a-space direction="horizontal" class="input-container">
      <a-input class="ant-input-certify" v-model:value="verificationCode" placeholder="输入验证码" />
      <a-button class="ant-input-sendc"  @click="sendVerificationCode">向原邮箱发送验证码</a-button>
    </a-space>

    <!-- 新邮箱输入行 -->
    <a-input v-model:value="newEmail" placeholder="输入新邮箱"  class="ant-input-certify" />

    <a-space direction="horizontal" class="input-container">
      <a-input class="ant-input-certify"  v-model:value="newVerificationCode" placeholder="输入验证码" />
      <a-button class="ant-input-sendc"  @click="sendNewVerificationCode">向新邮箱发送验证码</a-button>
    </a-space>

    <!-- 完成修改邮箱按钮 -->
    <a-button class="ucmail-fi" type="primary" @click="changeEmail">修改邮箱</a-button>
  </div>
</template>

<script>
export default {
  name: 'UserChangeEmail',
  data() {
    return {
      verificationCode: '',
      newEmail: '',
      trueCode: '',
      newVerificationCode: '',
      newTrueCode: ''
    };
  },
  methods: {
    sendVerificationCode() {
      // 实现发送验证码的逻辑
        this.$axios.post('/sendVerificationCode', {
          email: this.$store.state.email
        }).then((res) => {
          if (res.data.code === 200) {
            this.trueCode=res.data.verificationCode;
            this.$message.success('验证码发送成功');
          } else {
            console.error('验证码发送失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('验证码发送失败:', err);
        });
    },
    sendNewVerificationCode() {
      // 实现发送验证码的逻辑
        this.$axios.post('/sendVerificationCode', {
          email: this.newEmail
        }).then((res) => {
          if (res.data.code === 200) {
            this.newTrueCode=res.data.verificationCode;
            this.$message.success('验证码发送成功');
          } else {
            console.error('验证码发送失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('验证码发送失败:', err);
        });
    },
    changeEmail() {
      // TODO: 实现修改邮箱的逻辑
      if(this.verificationCode === this.trueCode && this.newVerificationCode === this.newTrueCode){
        this.$axios.post('/changeEmail', {
          email: this.newEmail,
          username: this.$store.state.username
        }).then((res) => {
          if (res.data.code === 200) {
            this.$message.success('邮箱修改成功');
            this.$store.dispatch('updateEmail', this.newEmail);
          } else {
            console.error('邮箱修改失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('邮箱修改失败:', err);
        });
      }
      else{
        this.$message.error('验证码错误');
      }
  }
  },
};
</script>

<style scoped>
.user-change-email-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-direction: column;
}

.input-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 24vw; /* 让这一行充满整个容器宽度 */
  margin: 10px; /* 调整间距根据需要 */
}
.ant-input-certify {
  font-size: 20px;
}
.ant-input-sendc{
  /* font-size: 20px;
  height:40px; */
  border: 1px solid #2f2f2f;
}
.user-change-email-container > .ant-input {
  width: 24vw; /* 让输入框宽度与验证码输入行一致 */
}

.user-change-email-container > .ant-input-password {
  width: 24vw; /* 让输入框宽度与验证码输入行一致 */
}

.user-change-email-container > .ant-input-number {
  width: 24vw; /* 让输入框宽度与验证码输入行一致 */
}
.user-change-email-container > * {
  margin: 20px;
}
.ucmail-fi{
  margin-top: 20px;
  font-size: 20px;
  height: 60px;
}

</style>
