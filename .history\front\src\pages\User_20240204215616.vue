<template>
  <div class="user-view-container">
    <div class="buttons-container">
        <a-button @click="goToMain" type="default" class="main-back">
        <HomeFilled  :style="{ fontSize: '40px' }"/>
        <UserOutlined :style="{ fontSize: '22px' }"/>
        {{ $store.state.username }}
      </a-button>
      <a-button @click="changeComponent('UserInfo')" :class="{ 'selected': currentComponent === 'UserInfo' }"><IdcardOutlined />个人信息</a-button>
      <a-button @click="changeComponent('UserChangePassword')" :class="{ 'selected': currentComponent === 'UserChangePassword' }"><InsuranceOutlined />修改密码</a-button>
      <a-button @click="changeComponent('UserChangeEmail')" :class="{ 'selected': currentComponent === 'UserChangeEmail' }"><MailOutlined />修改邮箱</a-button>
      <!-- 如果用户为管理员显示，否则不显示 -->
      <a-button v-if="$store.state.is_admin" @click="changeComponent('UserAdmin')" :class="{ 'selected': currentComponent === 'UserAdmin' }"><TeamOutlined />用户管理</a-button>
      <a-button @click="goToLogin" type="default" class="main-back" title="退出登录">
        <LoginOutlined :style="{ fontSize: '30px' }"/>
      </a-button>
    </div>
    
    <div class="current-component">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script>
import UserInfo from '../components/user_info.vue';
import UserChangePassword from '../components/user_ch_pwd.vue';
import UserChangeEmail from '../components/user_ch_mail.vue';
import UserAdmin from '../components/user_admin.vue';
import { UserOutlined, LoginOutlined ,HomeFilled,TeamOutlined,MailOutlined,IdcardOutlined,InsuranceOutlined} from '@ant-design/icons-vue';

export default {
  name: 'UserView',
  data() {
    return {
      currentComponent: 'UserInfo',
    };
  },
  methods: {
    changeComponent(componentName) {
      this.currentComponent = componentName;
    },
    goToLogin() {
      this.currentComponent = 'UserInfo';
      this.$router.push('/');
    },
    goToMain() {
      this.currentComponent = 'UserInfo';
      this.$router.push('/main');
    },
  },
  components: {
    UserInfo,
    UserChangePassword,
    UserChangeEmail,
    IdcardOutlined,
    TeamOutlined,
    UserAdmin,
    UserOutlined,
    LoginOutlined,
    HomeFilled,
    MailOutlined,
    InsuranceOutlined
  },
};
</script>

<style scoped>
.user-view-container {
  display: flex;
  width: 100vw;
  height: 100vh;
  background-color: #ffffff;
  justify-content: space-between;
  align-items: center;
}

.buttons-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: #f0f2f5;
  height: 100vh;
}

.buttons-container > .ant-btn {
  width: 20vw;
  border-radius: 0px;
  border: 0px;
}

.buttons-container > .ant-btn:last-child {
  margin-top: auto;
  margin-left: 0;
  margin-right: 0;
  font-size: 16px;
  font-family: 'Microsoft YaHei';
  line-height: 30px;
}
.buttons-container > .ant-btn:first-child {
  margin-top: 5vh;
  height: 10vh;
  position: relative;
  font-size: 20px;
  font-family: 'Microsoft YaHei';
  font-weight: bolder;
  border-bottom: 3px solid #083B66;
}

.buttons-container > .ant-btn:not(:last-child) {
  height: 10vh;
  position: relative;
  font-size: 20px;
  font-family: 'Microsoft YaHei';
  font-weight: bolder;
}

.buttons-container > .ant-btn{
  background-color: #f0f2f5;
}

.buttons-container > .selected {
  background-color: #141414; /* 蓝色表示选中状态 */
  color: white;
}

.current-component {
  flex: 4;
  margin-left: 20px; /* 调整 margin 根据需要 */
}
</style>
