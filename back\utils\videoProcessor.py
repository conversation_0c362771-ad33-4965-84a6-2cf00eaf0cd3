from moviepy.editor import VideoFileClip
from PIL import Image
import os
def analyseVideo(videoPath,segNum):
    # 获取视频时间
    Video = VideoFileClip(videoPath)
    duration = Video.duration
    # 关闭
    Video.close()
    
    # 计算每段视频的起始帧和结束帧
    segDuration = duration/segNum
    segTime = []
    for i in range(segNum):
        if (i+1)*segDuration<duration:
            segTime.append((int(i*segDuration),int((i+1)*segDuration)-1))
        else:
            segTime.append((int(i*segDuration),int(duration-1)))

    return segTime

# 将某些帧的图像保存到指定路径,命名为帧号加下划线加相似度,frameList的元素是二元组,第一个元素是时间戳,第二个元素是相似度,用moviepy
def saveFrame(videoPath, frameList, savePath):
    # 创建输出目录
    if not os.path.exists(savePath):
        os.mkdir(savePath)

    # 读取视频
    clip = VideoFileClip(videoPath)

    # 保存帧
    for timestamp, similarity in frameList:
        # 获取视频帧
        frame = clip.get_frame(timestamp)

        # 将帧转换为 PIL Image
        im = Image.fromarray(frame)

        # 构造保存文件名，格式为帧号_相似度.jpg
        filename = f"{timestamp}_{similarity}.jpg"

        # 保存图像
        im.save(os.path.join(savePath, filename))

    # 关闭视频文件
    clip.reader.close()