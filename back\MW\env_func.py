import random,time
import cv2
import torch
from PIL import Image
import numpy as np
def schedule(configData,videoData,model,preprocess,text,stopData):
    # 处理参数
    videoPath=videoData[0]
    fps=videoData[1]
    frameCount=videoData[2]
    startF=videoData[3]
    endF=videoData[4]

    STOPINTERVAL=stopData[0]
    STOPTHRESHOLD=stopData[1]
    CANDIDATENUM=stopData[2]  

    sampleList=[]


    # sampleList初始化
    video=cv2.VideoCapture(videoPath)
    frames=getFrames(video,[startF,endF])
    simList=calculateSim(frames,model,preprocess,text)
    sampleList.append([(startF,simList[0]),(endF,simList[1])])
    print(sampleList)

    future=calSampleList(sampleList)
    print(future)
    # 关闭视频
    video.release()
    return simList

def calculateSim(frameList,model,preprocess,text):
    # 计算相似度
    simList=[]
    for frame in frameList:
        if frame is None:
            simList.append(0)
            continue
        image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        image = preprocess(image).unsqueeze(0)
        with torch.no_grad():
            logits_per_image, _ = model(image,text)
        logits_per_image=logits_per_image.detach().numpy()
        simList.append(round(logits_per_image[0][0]/100,2))
    return simList

def getFrames(video,frameNumList):
    # 获取关键帧
    frameList=[]
    for frameNum in frameNumList:
        video.set(cv2.CAP_PROP_POS_FRAMES,frameNum)
        _,frame=video.read()
        frameList.append(frame)
    return frameList

def calSampleList(sampleList):
    nowSampleList=np.array(sampleList)[:,0]
    futureSampleList=np.array((nowSampleList[1:]+nowSampleList[:-1])/2,dtype=int)
    return futureSampleList



    