from flask import Flask, jsonify, request, send_file
from flask_cors import CORS
from celery import Celery
import json
import smtplib
from email.mime.text import MIMEText
import random
import clip
import traceback
import concurrent.futures
import numpy as np
import base64
from utils import dataProcessor,videoProcessor
from MW import env,expand
from DB.DBTools import DatabaseHelper
import os,time,shutil

os.chdir(os.path.dirname(__file__))
# from celery import Celery
# import time

app = Flask(__name__)
print(app.name)
CORS(app)
celery = Celery(
    app.name,
    backend='redis://127.0.0.1:6379/0',
    broker='redis://127.0.0.1:6379/0'
)
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024 * 1024


# 定义后台任务，主要是分析
@celery.task
def scan_balanced(task_id):
    task = DatabaseHelper.update_task_status(task_id, 'running')
    CLIPmodel,CLIPpreprocess=clip.load("ViT-B/32",device='cpu')
    
    config_data = task['task_config']
    maxConcurrent = config_data['MAC']
    segNum = config_data['NCS']
    
    # downSampleDen = config_data['DS']['DSD']

    randomWalk = config_data['RW']
    TerminalInterval = randomWalk['TI']
    # agentNum = randomWalk['NA']
    TerminalThreshold = randomWalk['TT']

    candidateNum = int(config_data['NCM'])
    # 读取完毕

    # 获取文本
    text = config_data['prompt']
    # 读取完毕

    # 获取视频路径
    video_name = config_data['video_name']
    video_path = os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), video_name)
    # 读取完毕

    # 如果存在目录先删除,frames未来存放关键帧，segs未来存放关键帧扩张后的视频片段
    if os.path.exists(os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'frames')):
        shutil.rmtree(os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'frames'))
    if os.path.exists(os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'segs')):
        shutil.rmtree(os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'segs'))
    # 删除完毕

    # 创建目录
    os.makedirs(os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'frames'))
    os.makedirs(os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'segs'))
    # 创建完毕

    # 读取视频
    taskEachsd = videoProcessor.analyseVideo(video_path, segNum)
    # 读取完毕

    # 读取CLIP模型
    text = clip.tokenize([text]).to('cpu')
    # 读取完毕

    # 结果存放在这里
    res = []
    # 创建进程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=maxConcurrent) as executor:
        # 提交任务
        tasks = [executor.submit(env.schedule,config_data,(video_path,st,nd),CLIPmodel,CLIPpreprocess,text,(TerminalInterval,TerminalThreshold,candidateNum)) for st,nd in taskEachsd]
        # 获取已完成的任务的返回值
        for future in concurrent.futures.as_completed(tasks):
            try:
                res.append(future.result())
            except Exception as e:
                print("Task raised an exception:", e)
                # 获取详细的错误信息
                traceback.print_exc()
    
    # 返回结果
    print(res)
    # 综合结果
    ultimateRes=[]
    for eachRes in res:
        for item in eachRes:
            ultimateRes.append(item)
    # ultimateRes是一个二元组列表，每个二元组的第一个元素是时间戳，第二个元素是相似度
    # 现在取得最大相似度的candidateNum个结果,先转成numpy数组
    ultimateRes=np.array(ultimateRes)
    # 按照相似度降序排列
    ultimateRes=ultimateRes[ultimateRes[:,1].argsort()[::-1]]
    # 取前candidateNum个,并删除其中相似度为0的,然后转成列表
    ultimateRes=ultimateRes[:candidateNum]
    ultimateRes=ultimateRes[ultimateRes[:,1]!=0]
    ultimateRes=ultimateRes.tolist()

    # 保存到tmp文件夹下面的frames文件夹
    videoProcessor.saveFrame(video_path,ultimateRes,os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'frames'))
    # 保存完毕
    expand.expand(video_path, ultimateRes, os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'segs'),CLIPmodel,CLIPpreprocess,text,TerminalThreshold,TerminalInterval)
    # 扩张关键帧完毕

    task = DatabaseHelper.update_task_status(task_id, 'finished')

    return 'i am back'
    

# 定义路由，接收请求时触发后台任务
@app.route('/RWScan', methods=['POST'])
def RWScan():
    # 异步调用后台任务
    # result = scan_balanced.delay()
    task = DatabaseHelper.update_task_status(request.json.get('task_id'), 'running')
    result = scan_balanced.apply_async(args=[request.json.get('task_id')])
    # 更新任务编号
    task=DatabaseHelper.update_task_number(request.json.get('task_id'),result.id)
    return jsonify({"task": task, "status": "Task started. Check status with /status/<task_id>"})

# 定义路由，检查后台任务状态
@app.route('/status/<task_id>', methods=['GET'])
def task_status(task_id):
    task = scan_balanced.AsyncResult(task_id)
    if task.state == 'PENDING':
        response = {"code":"PENDING","status": "Task not yet started"}
    elif task.state == 'SUCCESS':
        response = {"code":"SUCCESS","status": "Task completed", "result": task.result}
    elif task.state == 'FAILURE':
        response = {"code":"FAILURE","status": "Task failed", "error": str(task.result)}
    else:
        response = {"code":"OTHER","status": task.state}

    return jsonify(response)

# 验证用户登录
@app.route('/login', methods=['POST'])
def login():
    #从请求中获取用户名和密码
    username = request.json.get('username')
    password = request.json.get('password')
    #在数据库中查找用户
    user = DatabaseHelper.get_user_by_username(username)
    print(user)
    if user is None:
        return jsonify({'code': 404,'message':'用户不存在'})
    else:
        if user['password'] == password:
            return jsonify({'code': 200, 'user': user})
        else:
            return jsonify({'code': 300, 'message': '密码错误'})    

# 验证用户注册
@app.route('/register', methods=['POST'])
def register():
    #从请求中获取用户名和密码
    username = request.json.get('username')
    password = request.json.get('password')
    email = request.json.get('email')
    #在数据库中查找用户
    user = DatabaseHelper.get_user_by_username(username)
    if user is not None:
        return jsonify({'code': 300, 'message': '用户已存在'})
    else:
        user_id = DatabaseHelper.add_user(username, password, email)
        return jsonify({'code': 200, 'user_id': user_id})

# 修改密码
@app.route('/changePassword', methods=['POST'])
def changePassword():
    #从请求中获取用户名和密码
    username = request.json.get('username')
    password = request.json.get('password')
    #在数据库中查找用户
    user = DatabaseHelper.get_user_by_username(username)
    if user is None:
        return jsonify({'code': 404, 'message': '用户不存在'})
    else:
        user_id = DatabaseHelper.change_user_password(username, password)
        return jsonify({'code': 200, 'user_id': user_id})

# 修改邮箱
@app.route('/changeEmail', methods=['POST'])
def changeEmail():
    #从请求中获取用户名和密码
    username = request.json.get('username')
    email = request.json.get('email')
    #在数据库中查找用户
    user = DatabaseHelper.get_user_by_username(username)
    if user is None:
        return jsonify({'code': 404, 'message': '用户不存在'})
    else:
        user_id = DatabaseHelper.change_user_email(username, email)
        return jsonify({'code': 200, 'user_id': user_id})

# 获取所有用户
@app.route('/getAllUsers', methods=['GET'])
def getAllUsers():
    users = DatabaseHelper.get_all_users()
    return jsonify({'code': 200, 'users': users})

# 删除用户
@app.route('/deleteUser', methods=['POST'])
def deleteUser():
    #从请求中获取用户名和密码
    username = request.json.get('username')
    #在数据库中查找用户
    user = DatabaseHelper.get_user_by_username(username)
    if user is None:
        return jsonify({'code': 404, 'message': '用户不存在'})
    else:
        user_id = DatabaseHelper.delete_user(username)
        return jsonify({'code': 200, 'user_id': user_id})

# 更新用户
@app.route('/updateUsers', methods=['POST'])
def updateUsers():
    #从请求中获取用户名和密码
    users = request.json.get('users')
    #在数据库中查找用户
    user_id = DatabaseHelper.update_users(users)
    return jsonify({'code': 200, 'user_id': user_id})

# 发送验证码
@app.route('/sendVerificationCode', methods=['POST'])
def sendVerificationCode():
    
    # 邮件服务器信息
    smtp_server = 'smtp.qq.com'
    smtp_port = 587
    smtp_username = '<EMAIL>'
    smtp_password = 'szqjlvfcmexedicj'  # 这里填写你的QQ邮箱授权码

    # 发件人和收件人信息
    sender_email = '<EMAIL>'
    #从请求中获取邮箱
    receiver_email = request.json.get('email')
    # 生成验证码，包含数字和字母的随机组合，总共六位
    code = ''.join(random.sample('1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 6))
    # 邮件内容
    subject = '系统验证'
    body = '您好, 您于'+time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())+'关于长时视频定位系统的验证码为'+code

    # 创建邮件消息
    message = MIMEText(body, 'plain', 'utf-8')
    message['From'] = sender_email  # 直接设置发件人地址
    message['To'] = receiver_email
    message['Subject'] = subject
    # 连接到SMTP服务器并发送邮件
    try:
        smtp = smtplib.SMTP(smtp_server, smtp_port)
        smtp.starttls()  # 使用TLS加密连接
        smtp.login(smtp_username, smtp_password)
        smtp.sendmail(sender_email, receiver_email, message.as_string())
        smtp.quit()
        print("邮件发送成功！")
        return jsonify({'code': 200, 'message': '邮件发送成功', 'verificationCode': code})
    except Exception as e:
        print(f"邮件发送失败：{e}")
        return jsonify({'code': 404, 'message': '邮件发送失败'})

# 获取用户的所有任务
@app.route('/getTaskByUser', methods=['POST'])
def getTaskByUser():
    username = request.json.get('username')
    tasks = DatabaseHelper.get_task_by_username(username)
    for task in tasks:
        task['update_date'] = task['update_date'].strftime('%Y-%m-%d %H:%M:%S')
        task['create_date'] = task['create_date'].strftime('%Y-%m-%d %H:%M:%S')
        task['task_config'] = json.loads(task['task_config'])
    return jsonify({'code': 200, 'tasks': tasks})

# 获取任务详情
@app.route('/taskDetail', methods=['POST'])
def getTaskByID():
    task_id = request.json.get('task_id')
    task = DatabaseHelper.get_task_by_id(task_id)
    task['update_date'] = task['update_date'].strftime('%Y-%m-%d %H:%M:%S')
    task['create_date'] = task['create_date'].strftime('%Y-%m-%d %H:%M:%S')
    task['task_config'] = json.loads(task['task_config'])
    return jsonify({'code': 200, 'task': task})

# 创建任务
@app.route('/createTask', methods=['POST'])
def createTask():
    username = request.json.get('username')
    task_mode = request.json.get('task_mode')
    task_description = request.json.get('task_description')
    task_id = DatabaseHelper.add_task(username, task_mode, task_description)
    # 在Data文件夹下创建一个以task_id命名的文件夹
    os.makedirs('Data/'+username+'/'+str(task_id))
    return jsonify({'code': 200, 'task_id': task_id})

# 删除任务
@app.route('/deleteTask', methods=['POST'])
def deleteTask():
    task_id = request.json.get('task_id')
    username = request.json.get('username')
    task_id = DatabaseHelper.delete_task(task_id)
    # 删除Data文件夹下以task_id命名的非空文件夹
    shutil.rmtree('Data/'+username+'/'+str(task_id))
    return jsonify({'code': 200, 'task_id': task_id})

# 更新任务时间
@app.route('/updateTaskTime', methods=['POST'])
def taskDetail():
    task_id = request.json.get('task_id')
    task = DatabaseHelper.update_task_time(task_id)
    return jsonify({'code': 200, 'task': task})



# 接收视频并存储  
@app.route('/InputVideo', methods=['POST'])
def input_video():
    try:
        # 获取上传的文件
        video_file = request.files['video']
        task_id=request.form.get('task_id')
        task=DatabaseHelper.get_task_by_id(task_id)
        # 确保tmp文件夹存在，如果不存在则创建
        tmp_folder = os.path.join(os.getcwd(), 'Data',task['username'], str(task_id))
        if not os.path.exists(tmp_folder):
            os.makedirs(tmp_folder)

        # 设置文件名为recv+原视频格式后缀
        video_filename = 'recv' + os.path.splitext(video_file.filename)[1]

        # 保存文件到tmp文件夹
        video_path = os.path.join(tmp_folder, video_filename)
        video_file.save(video_path)

        # 在控制台打印文件保存路径
        print('Video saved at:', video_path)
        task_config=json.loads(task['task_config'])
        task_config['video_name']=video_filename
        task=DatabaseHelper.update_task_config(task_id,task_config)

        # 返回成功的JSON响应
        return jsonify({'message': 'Video received and saved successfully','task':task})
    except Exception as e:
        print('Error processing video:', str(e))
        return jsonify({'error': 'Failed to process video'}), 500
    

# 接收配置文件并存储
@app.route('/InputSettings',methods=['POST'])
def input_settings():
    task_id = request.json.get('task_id')
    task_config = request.json.get('task_config')
    task = DatabaseHelper.update_task_config(task_id,task_config)
    return jsonify({'code': 200, 'task': task})


# 返回视频数据
# 返回视频数据
@app.route('/DownloadVideo', methods=['GET'])
def get_video():
    task_id = request.args.get('task_id')
    task = DatabaseHelper.get_task_by_id(task_id)
    video_name = json.loads(task['task_config'])['video_name']
    video_path = os.path.join(os.getcwd(), 'Data', task['username'], str(task_id), video_name)

    # 确定文件格式
    file_format = dataProcessor.get_file_format(video_name)

    return send_file(
        video_path,
        mimetype=f'video/{file_format}',
        as_attachment=True,
        download_name=f'{video_name}'
    )

# 展示结果
@app.route('/showResult', methods=['POST'])
def showResult():
    task_id = request.json.get('task_id')
    task = DatabaseHelper.get_task_by_id(task_id)
    frames_directory = os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'frames')
    segs_directory = os.path.join(os.getcwd(), 'Data',task['username'], str(task_id), 'segs')
    print(frames_directory)

    data = {}

    # 处理 frames 目录下的图片
    frames_data = dataProcessor.process_directory(frames_directory)
    for key, value in frames_data.items():
        data[key] = {'frame': value}

    # 处理 segs 目录下的视频
    segs_data = dataProcessor.process_directory(segs_directory)
    for key, value in segs_data.items():
        data[key]['video'] = value

    reData = {}
    for key, value in data.items():
        reData[key]=(value['frame']['similarity'],value['frame']['content'],value['frame']['format'],value['video']['content'],value['video']['format'])
    
    
    return jsonify(reData)
if __name__ == '__main__':
    app.run(debug=True)