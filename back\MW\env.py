import random,time
import cv2
from moviepy.editor import VideoFileClip
import torch
from PIL import Image
import numpy as np
def schedule(configData,videoData,model,preprocess,text,stopData):
    # 处理参数
    videoPath=videoData[0]
    startF=videoData[1]
    endF=videoData[2]

    STOPINTERVAL=stopData[0]
    STOPTHRESHOLD=stopData[1]
    CANDIDATENUM=stopData[2]  

    print("开始处理视频片段：",videoPath,"，起始帧：",startF,"，终止帧：",endF)
    # sampleList初始化
    video=VideoFileClip(videoPath)
    frames=getFrames(video,[startF,endF])
    simList=calculateSim(frames,model,preprocess,text)
    sampleList=[(startF,simList[0]),(endF,simList[1])]
    eachJudge=judgeStop(sampleList,STOPINTERVAL,STOPTHRESHOLD,CANDIDATENUM)
    sampleList=eachJudge[1]
    while eachJudge[0] == 0:
        # 计算未来采样帧列表
        futureFs=calSampleList(sampleList)
        # 读取未来采样帧
        futureFrames=getFrames(video,futureFs)
        # 计算未来采样帧的相似度
        futureSimList=calculateSim(futureFrames,model,preprocess,text)
        # 将未来采样帧的相似度加入sampleList
        sampleList.extend([(futureFs[i],futureSimList[i]) for i in range(len(futureFs))])
        # 判断是否停止
        eachJudge=judgeStop(sampleList,STOPINTERVAL,STOPTHRESHOLD,CANDIDATENUM)
        sampleList=eachJudge[1]
    # 关闭视频
    video.close()
    return sampleList

def calculateSim(frameList,model,preprocess,text):
    simList=[]
    for frame in frameList:
        if frame is None:
            simList.append(0)
            continue
        image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        image = Image.fromarray(image)
        image = preprocess(image).unsqueeze(0)
        with torch.no_grad():
            logits_per_image, _ = model(image,text)
        logits_per_image=logits_per_image.detach().numpy()
        simList.append(round(logits_per_image[0][0]/100,4))
    return simList

def getFrames(video,tsNumList):
    frameList=[]
    for frameNum in tsNumList:
        try:
            frame=video.get_frame(frameNum)
        except:
            frame=None
        frameList.append(frame)
    return frameList

def calSampleList(sampleList):
    nowSampleList=np.array(sampleList)[:,0]
    futureSampleList=np.array((nowSampleList[1:]+nowSampleList[:-1])/2,dtype=int).tolist()
    return futureSampleList

def judgeStop(sampleList,STOPINTERVAL,STOPTHRESHOLD,CANDIDATENUM):
    sampleList=np.array(sampleList)
    sampleList=sampleList[np.argsort(sampleList[:,1])[::-1]]
    if len(sampleList)>=CANDIDATENUM:
        if sampleList[CANDIDATENUM-1,1]>STOPTHRESHOLD:
            sampleList=sampleList[np.argsort(sampleList[:,0])]
            return 1,sampleList.tolist()
    
    # 相似度判断完毕


    sampleList=sampleList[np.argsort(sampleList[:,0])]
    print(np.diff(sampleList[:,0]))
    if np.all(np.diff(sampleList[:,0])<STOPINTERVAL):
        return 1,sampleList.tolist()
    
    # 间隔判断完毕
    
    return 0,sampleList.tolist()
    