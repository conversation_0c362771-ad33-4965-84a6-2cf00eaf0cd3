<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="smallGrid" width="10" height="10" patternUnits="userSpaceOnUse">
      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#1890ff" stroke-width="0.5" stroke-opacity="0.1"/>
    </pattern>
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="url(#smallGrid)"/>
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="#1890ff" stroke-width="1" stroke-opacity="0.2"/>
    </pattern>
  </defs>
  
  <rect width="100" height="100" fill="#f0f7ff"/>
  <rect width="100" height="100" fill="url(#grid)"/>
  
  <!-- City skyline silhouette -->
  <path d="M0,70 L5,70 L5,65 L10,65 L10,70 L15,70 L15,60 L20,60 L20,65 L25,65 L25,55 L30,55 L30,65 L35,65 L35,60 L40,60 L40,50 L45,50 L45,60 L50,60 L50,55 L55,55 L55,65 L60,65 L60,55 L65,55 L65,60 L70,60 L70,50 L75,50 L75,55 L80,55 L80,65 L85,65 L85,60 L90,60 L90,65 L95,65 L95,70 L100,70 L100,100 L0,100 Z" fill="#1890ff" fill-opacity="0.1"/>
  
  <!-- Abstract data visualization elements -->
  <circle cx="20" cy="30" r="5" fill="#52c41a" fill-opacity="0.2"/>
  <circle cx="70" cy="40" r="8" fill="#1890ff" fill-opacity="0.2"/>
  <circle cx="40" cy="20" r="3" fill="#faad14" fill-opacity="0.2"/>
  <circle cx="85" cy="25" r="4" fill="#f5222d" fill-opacity="0.2"/>
  
  <!-- Connection lines -->
  <line x1="20" y1="30" x2="40" y2="20" stroke="#52c41a" stroke-width="1" stroke-opacity="0.2"/>
  <line x1="40" y1="20" x2="70" y2="40" stroke="#1890ff" stroke-width="1" stroke-opacity="0.2"/>
  <line x1="70" y1="40" x2="85" y2="25" stroke="#faad14" stroke-width="1" stroke-opacity="0.2"/>
  <line x1="85" y1="25" x2="20" y2="30" stroke="#f5222d" stroke-width="1" stroke-opacity="0.2"/>
</svg>
