from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from datetime import datetime
import json
from DB.initDB import User, Task  # Assuming your models are in a file named models.py

class DatabaseHelper:
    # 类级别的变量，用于存储数据库引擎和会话
    engine = create_engine('sqlite:///DB/database.db', echo=True)
    Session = sessionmaker(bind=engine)
    NULLTASK = 'NULLTASK'

    # 不同模式的配置
    task_config = {
        'efficient':{
            'model':'efficient',
            'batch_size':32,
            'epochs':10,
            'learning_rate':0.01
        },
        'accurate':{
            'model':'accurate',
            'batch_size':64,
            'epochs':20,
            'learning_rate':0.001
        },
        'balanced':{
            "MAC": 2,
            # 最大并发数量
            "NCS": 2,
            # 分段数量
            "RW": {
                "TI": 40,
                # 终止间隔
                "TT": 0.3
                # 终止阈值
            },
            "NCM": 4,
            # 希望生成的候选段数量
            "video_name": "",
            # 视频路径
            "prompt": ""
            # 提示语
        }
    }


    @classmethod
    def get_user_by_username(cls, username):
        with cls.Session() as session:
            user = session.query(User).filter_by(username=username).first()
            if user is None:
                return None
            ret= {
                'id':user.id,
                'username':user.username,
                'password':user.password,
                'email':user.email,
                'is_admin':user.is_admin
            }
            return ret

    @classmethod
    def add_user(cls, username, password, email, is_admin=False):
        with cls.Session() as session:
            user = User(username=username, password=password, email=email, is_admin=is_admin)
            session.add(user)
            session.commit()
            return user.id
    
    @classmethod
    def change_user_password(cls, username, password):
        with cls.Session() as session:
            user = session.query(User).filter_by(username=username).first()
            if user is None:
                return None
            user.password = password
            session.commit()
            return user.id
        
    @classmethod
    def change_user_email(cls, username, email):
        with cls.Session() as session:
            user = session.query(User).filter_by(username=username).first()
            if user is None:
                return None
            user.email = email
            session.commit()
            return user.id
    
    @classmethod
    def delete_user(cls, username):
        with cls.Session() as session:
            user = session.query(User).filter_by(username=username).first()
            if user is None:
                return None
            session.delete(user)
            session.commit()
            return user.id

    @classmethod
    def update_users(cls, users):
        with cls.Session() as session:
            for this_user in users:
                user = session.query(User).filter_by(username=this_user['username']).first()
                if user is None:
                    continue
                user.password = this_user['password']
                user.email = this_user['email']
                user.is_admin = this_user['is_admin']
                session.commit()
            return True
    
    @classmethod
    def get_all_users(cls):
        with cls.Session() as session:
            users = session.query(User).all()
            ret = []
            for user in users:
                ret.append({
                    'id':user.id,
                    'username':user.username,
                    'password':user.password,
                    'email':user.email,
                    'is_admin':user.is_admin
                })
            return ret

    @classmethod
    def get_task_by_username(cls, username):
        with cls.Session() as session:
            tasks = session.query(Task).filter_by(username=username).all()
            ret = []
            for task in tasks:
                ret.append({
                    'id':task.id,
                    'username':task.username,
                    'task_number':task.task_number,
                    'update_date':task.update_date,
                    'create_date':task.create_date,
                    'task_mode':task.task_mode,
                    'task_status':task.task_status,
                    'task_config':task.task_config,
                    'task_description':task.task_description
                })
            return ret
        
    # 以上是用户相关的操作，以下是任务相关的操作
        
    @classmethod
    def get_task_by_id(cls, task_id):
        with cls.Session() as session:
            task = session.query(Task).filter_by(id=task_id).first()
            if task is None:
                return None
            ret = {
                'id':task.id,
                'username':task.username,
                'task_number':task.task_number,
                'update_date':task.update_date,
                'create_date':task.create_date,
                'task_mode':task.task_mode,
                'task_status':task.task_status,
                'task_config':task.task_config,
                'task_description':task.task_description
            }
            return ret

    @classmethod
    def add_task(cls, username,  task_mode,  task_description):
        with cls.Session() as session:
            thisTime = datetime.now()
            task = Task(username=username, task_number=cls.NULLTASK, task_mode=task_mode, task_status="preparing", task_config=json.dumps(cls.task_config[task_mode]), task_description=task_description, create_date=thisTime, update_date=thisTime)
            session.add(task)
            session.commit()
            return task.id
        
    @classmethod
    def delete_task(cls, task_id):
        with cls.Session() as session:
            task = session.query(Task).filter_by(id=task_id).first()
            if task is None:
                return None
            session.delete(task)
            session.commit()
            return task.id
        
    @classmethod
    def update_task_time(cls, task_id):
        with cls.Session() as session:
            task = session.query(Task).filter_by(id=task_id).first()
            if task is None:
                return None
            task.update_date = datetime.now()
            session.commit()
            ret={
                'id':task.id,
                'username':task.username,
                'task_number':task.task_number,
                'update_date':task.update_date,
                'create_date':task.create_date,
                'task_mode':task.task_mode,
                'task_status':task.task_status,
                'task_config':json.loads(task.task_config),
                'task_description':task.task_description
            }
            return ret
        
    @classmethod
    def update_task_config(cls, task_id, task_config):
        with cls.Session() as session:
            task = session.query(Task).filter_by(id=task_id).first()
            if task is None:
                return None
            task.task_config = json.dumps(task_config)
            session.commit()
            ret={
                'id':task.id,
                'username':task.username,
                'task_number':task.task_number,
                'update_date':task.update_date,
                'create_date':task.create_date,
                'task_mode':task.task_mode,
                'task_status':task.task_status,
                'task_config':json.loads(task.task_config),
                'task_description':task.task_description
            }
            return ret
        
    @classmethod
    def update_task_status(cls, task_id, task_status):
        with cls.Session() as session:
            task = session.query(Task).filter_by(id=task_id).first()
            if task is None:
                return None
            task.task_status = task_status
            session.commit()
            ret={
                'id':task.id,
                'username':task.username,
                'task_number':task.task_number,
                'update_date':task.update_date,
                'create_date':task.create_date,
                'task_mode':task.task_mode,
                'task_status':task.task_status,
                'task_config':json.loads(task.task_config),
                'task_description':task.task_description
            }
            return ret

    @classmethod
    def update_task_number(cls, task_id, task_number):
        with cls.Session() as session:
            task = session.query(Task).filter_by(id=task_id).first()
            if task is None:
                return None
            task.task_number = task_number
            session.commit()
            ret={
                'id':task.id,
                'username':task.username,
                'task_number':task.task_number,
                'update_date':task.update_date,
                'create_date':task.create_date,
                'task_mode':task.task_mode,
                'task_status':task.task_status,
                'task_config':json.loads(task.task_config),
                'task_description':task.task_description
            }
            return ret
# 在其他文件中使用：
# user_result = DatabaseHelper.get_user_by_username('admin')
# task_result = DatabaseHelper.get_task_by_id(1)
