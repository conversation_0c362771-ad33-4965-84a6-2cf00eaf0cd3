// main.js
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store'; // 引入store
import Antd from 'ant-design-vue';
import axios from 'axios';

axios.defaults.baseURL = 'http://localhost:5000'; 

const app = createApp(App);
app.use(router);
app.use(Antd); // 使用Ant Design Vue
app.use(store); // 使用store
app.config.globalProperties.$axios = axios;
app.mount('#app');
