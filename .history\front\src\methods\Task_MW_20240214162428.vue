<template>
  <div>
    <div class="header">
        <a-button type="default" @click="returnUp"><ArrowLeftOutlined /></a-button>
        <a-button type="default" @click="returnMain"><HomeFilled/></a-button>
        <a-button type="default" @click="logOut"><LogoutOutlined /></a-button>
        <img src="../assets/hitsz.png" class="hitsz-logo">
    </div>
    <div id="mw">
      <div class="col1 inputs">
          <Inputs v-if="shouldRender"  :setTs="setTs" :atTs="atTs" :setTsFunc="setTsFunc"/>
      </div>
      <div class="col2 results">
        <Results v-if="shouldRender" :changeTs="changeTs" :setTsFunc="setTsFunc"/>
      </div>
    </div>
    <div v-if="$store.state.uploading" class="uploading-overlay">
        <div class="uploading-spinner"></div>
        <p>稍等, 上传中...</p>
    </div>

    <div v-if="$store.state.task_ready" class="uploading-overlay">
        <div class="uploading-spinner"></div>
        <p>任务下载中...</p>
    </div>
      
    <!-- 如果 $store.state.task_record.task_status等于“running”，显示遮罩-->
    <div v-if="$store.state.task_record.task_status === 'running'" class="taskRun-overlay">
        <div class="taskRun-spinner"></div>
        <p>任务运行中...</p>
    </div>
  </div>
</template>

<script>
import Inputs from './MW/Inputs.vue';
import Results from './MW/Results.vue';
import { HomeFilled, LogoutOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';

export default {
  name: 'MWTaskView',
  data() {
    return {
      shouldRender: true,
      // 视频操控
      scan_signal: false,
      setTs: false,
      atTs: 0.0
    };
  },
  components: {
    Inputs,
    Results,
    HomeFilled,
    LogoutOutlined,
    ArrowLeftOutlined,
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.shouldRender = true;
    });
  },
  methods: {
    setTsFunc(newSignal) {
      this.setTs = newSignal;
    },
    changeTs(ts) {
      this.atTs = Number(ts);
    },
    logOut() {
      this.shouldRender = false; // 设置 shouldRender 为 false，销毁组件
      this.$router.push({ path: '/' });
    },
    returnMain() {
      this.shouldRender = false;
      console.log(this.$store.state.task_record);
      this.$router.push({ path: '/main' });
    },
    returnUp() {
      this.shouldRender = false;
      this.$router.push({ path: '/task' });
    },
  },
};
</script>

  
  <style>
  .hitsz-logo {
    height: 67px;
    width: 365px;
    position: absolute;
    right: 0;
  }
  .header {
    display: flex;
    flex-direction: row;
    align-items: center;
    /* 左对齐 */
    justify-content: flex-start;
    width: 100vw;
    height: 10vh;
    background-color: transparent;
    padding-left: 2vw;
    background-color: #2C3D50;
    opacity: 0.96;
    border-bottom: 3px solid #FFFEFC;
    position: relative;
    z-index: 10000;
  }
  .header > * {
    margin: 30px 10px;
  }
  #mw {
    display: flex;
    flex-direction: row;
    width: 100vw;
    height: 90vh;
  }
  
  
  .col1 {
    width: 40vw;
    height: 100vh;
    background-color: #2C3D50;
    opacity: 0.96;
    display: flex;
    flex-direction: column;
  }

  .col2 {
    width: 60vw;
    background-color: #2C3D50;
    opacity: 0.96;
  }


  
.uploading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #2C3D50;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10001;
}

.uploading-overlay p {
  color: #FFFEFC;
  font-size: 24px;
  margin-top: 20px;
}

.uploading-spinner {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



  
.taskRun-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #2C3D50;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9998;
}

.taskRun-overlay p {
  color: #FFFEFC;
  font-size: 24px;
  margin-top: 20px;
}

.taskRun-spinner {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


/* 在你的样式表中添加以下样式 */
.video::-webkit-media-controls-fullscreen-button {
  display: none !important;
}
  </style>
  