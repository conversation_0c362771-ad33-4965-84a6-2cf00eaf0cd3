<template>
  <div class="user-view-container">
    <div class="sidebar fade-in">
      <div class="user-profile">
        <a-avatar :size="80" icon="user" class="user-avatar">
          <template #icon><UserOutlined /></template>
        </a-avatar>
        <h2 class="username">{{ $store.state.username }}</h2>
        <p class="user-role">{{ $store.state.is_admin ? '管理员' : '普通用户' }}</p>
      </div>

      <div class="menu-items">
        <a-button
          @click="changeComponent('UserInfo')"
          :class="['menu-item', { 'selected': currentComponent === 'UserInfo' }]"
          type="text"
        >
          <IdcardOutlined />
          <span>个人信息</span>
        </a-button>

        <a-button
          @click="changeComponent('UserChangePassword')"
          :class="['menu-item', { 'selected': currentComponent === 'UserChangePassword' }]"
          type="text"
        >
          <InsuranceOutlined />
          <span>修改密码</span>
        </a-button>

        <a-button
          @click="changeComponent('UserChangeEmail')"
          :class="['menu-item', { 'selected': currentComponent === 'UserChangeEmail' }]"
          type="text"
        >
          <MailOutlined />
          <span>修改邮箱</span>
        </a-button>

        <a-button
          v-if="$store.state.is_admin"
          @click="changeComponent('UserAdmin')"
          :class="['menu-item', { 'selected': currentComponent === 'UserAdmin' }]"
          type="text"
        >
          <TeamOutlined />
          <span>用户管理</span>
        </a-button>
      </div>

      <div class="bottom-actions">
        <a-button @click="goToMain" type="primary" class="action-button home-button">
          <HomeFilled />
          <span>返回首页</span>
        </a-button>

        <a-button @click="goToLogin" type="danger" class="action-button logout-button">
          <LoginOutlined />
          <span>退出登录</span>
        </a-button>
      </div>
    </div>

    <div class="content-area slide-in-right">
      <div class="content-header">
        <h1 class="page-title">
          <span v-if="currentComponent === 'UserInfo'">个人信息</span>
          <span v-else-if="currentComponent === 'UserChangePassword'">修改密码</span>
          <span v-else-if="currentComponent === 'UserChangeEmail'">修改邮箱</span>
          <span v-else-if="currentComponent === 'UserAdmin'">用户管理</span>
        </h1>
      </div>

      <div class="content-body">
        <component :is="currentComponent" />
      </div>
    </div>
  </div>
</template>

<script>
import UserInfo from '../components/user_info.vue';
import UserChangePassword from '../components/user_ch_pwd.vue';
import UserChangeEmail from '../components/user_ch_mail.vue';
import UserAdmin from '../components/user_admin.vue';
import { UserOutlined, LoginOutlined ,HomeFilled,TeamOutlined,MailOutlined,IdcardOutlined,InsuranceOutlined} from '@ant-design/icons-vue';

export default {
  name: 'UserView',
  data() {
    return {
      currentComponent: 'UserInfo',
    };
  },
  methods: {
    changeComponent(componentName) {
      this.currentComponent = componentName;
    },
    goToLogin() {
      this.currentComponent = 'UserInfo';
      this.$router.push('/');
    },
    goToMain() {
      this.currentComponent = 'UserInfo';
      this.$router.push('/main');
    },
  },
  components: {
    UserInfo,
    UserChangePassword,
    UserChangeEmail,
    IdcardOutlined,
    TeamOutlined,
    UserAdmin,
    UserOutlined,
    LoginOutlined,
    HomeFilled,
    MailOutlined,
    InsuranceOutlined
  },
};
</script>

<style scoped>
.user-view-container {
  display: flex;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 247, 255, 0.85) 100%);
  backdrop-filter: blur(10px);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  margin: 20px;
  position: relative;
}

.user-view-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%231890ff' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
  color: white;
  display: flex;
  flex-direction: column;
  padding: 30px 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.user-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
}

.user-avatar {
  margin-bottom: 15px;
  background-color: #4299e1;
  border: 3px solid rgba(255, 255, 255, 0.2);
}

.username {
  font-size: 22px;
  font-weight: 600;
  margin: 10px 0 5px;
  color: white;
}

.user-role {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.menu-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px 15px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  margin-bottom: 10px;
  border-radius: 8px;
  transition: all 0.3s;
  color: rgba(255, 255, 255, 0.8);
  background: transparent;
  border: none;
  text-align: left;
  font-size: 16px;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(5px);
}

.menu-item.selected {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
}

.menu-item .anticon {
  margin-right: 12px;
  font-size: 18px;
}

.bottom-actions {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s;
  font-size: 16px;
}

.action-button .anticon {
  margin-right: 8px;
}

.home-button {
  background-color: #4299e1;
  border-color: #4299e1;
}

.home-button:hover {
  background-color: #3182ce;
  border-color: #3182ce;
  transform: translateY(-2px);
}

.logout-button {
  background-color: #f56565;
  border-color: #f56565;
}

.logout-button:hover {
  background-color: #e53e3e;
  border-color: #e53e3e;
  transform: translateY(-2px);
}

/* 内容区域样式 */
.content-area {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(245, 250, 255, 0.9) 100%);
  position: relative;
  z-index: 1;
}

.content-area::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%234299e1' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
  z-index: -1;
  opacity: 0.7;
  pointer-events: none;
}

.content-header {
  margin-bottom: 30px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  padding-bottom: 15px;
  position: relative;
}

.content-header::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(to right, #1890ff, #52c41a);
  border-radius: 3px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
  background: linear-gradient(to right, #1890ff, #52c41a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.content-body {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  min-height: 500px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  position: relative;
  overflow: hidden;
}

.content-body::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, #4776E6, #8E54E9);
  opacity: 0.7;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .user-view-container {
    flex-direction: column;
    margin: 0;
    height: auto;
    min-height: 100vh;
  }

  .sidebar {
    width: 100%;
    padding: 20px 0;
  }

  .content-area {
    padding: 20px;
  }
}
</style>
