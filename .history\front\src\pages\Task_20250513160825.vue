<template>
  <div class="task-container">
    <div class="task-header">
      <div class="header-left fade-in">
        <a-button @click="returnMain" type="text" class="nav-button">
          <HomeFilled />
          返回首页
        </a-button>
        <h1 class="page-title">城市监测任务</h1>
      </div>
      <div class="header-right fade-in">
        <div class="search-box">
          <a-input
            placeholder="搜索监测任务..."
            v-model:value="search"
            class="search-input"
          >
            <template #prefix><SearchOutlined /></template>
          </a-input>
          <a-button type="primary" @click="searchTask" class="search-button">
            搜索
          </a-button>
        </div>
        <a-button type="primary" @click="getTasks" class="refresh-button">
          <ReloadOutlined />
          刷新
        </a-button>
        <a-button type="primary" @click="addTask" class="add-button">
          <PlusOutlined />
          新建监测
        </a-button>
        <a-button @click="logOut" type="text" class="logout-button">
          <LogoutOutlined />
        </a-button>
      </div>
    </div>

    <div class="task-content slide-in-up">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <div class="stat-card fade-in">
          <div class="stat-icon total-icon">
            <FileOutlined />
          </div>
          <div class="stat-info">
            <h3>总监测数</h3>
            <p class="stat-value">{{ dataSource.length }}</p>
          </div>
        </div>

        <div class="stat-card fade-in delay-1">
          <div class="stat-icon running-icon">
            <LoadingOutlined />
          </div>
          <div class="stat-info">
            <h3>处理中</h3>
            <p class="stat-value">{{ getStatusCount('running') }}</p>
          </div>
        </div>

        <div class="stat-card fade-in delay-2">
          <div class="stat-icon finished-icon">
            <CheckCircleOutlined />
          </div>
          <div class="stat-info">
            <h3>已完成</h3>
            <p class="stat-value">{{ getStatusCount('finished') }}</p>
          </div>
        </div>

        <div class="stat-card fade-in delay-3">
          <div class="stat-icon preparing-icon">
            <ClockCircleOutlined />
          </div>
          <div class="stat-info">
            <h3>待处理</h3>
            <p class="stat-value">{{ getStatusCount('preparing') }}</p>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <div class="table-container">
        <div class="table-header">
          <h2 class="section-title">监测任务列表</h2>
        </div>

        <!-- 表格视图 -->
        <a-table
          :dataSource="dataSource"
          :columns="columns"
          :pagination="{
            showSizeChanger: false,
            defaultPageSize: 8,
            showTotal: total => `共 ${total} 条记录`
          }"
          class="task-table"
          :rowClassName="() => 'task-row'"
        >
          <template v-slot:bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'task_mode'">
              <a-tag v-if="record.task_mode === 'balanced'" color="blue" class="mode-tag">综合监测</a-tag>
              <a-tag v-else-if="record.task_mode === 'efficient'" color="green" class="mode-tag">快速监测</a-tag>
              <a-tag v-else-if="record.task_mode === 'accurate'" color="red" class="mode-tag">精确监测</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'task_status'">
              <a-tag v-if="record.task_status === 'preparing'" color="blue" class="status-tag">待处理</a-tag>
              <a-tag v-else-if="record.task_status === 'running'" color="orange" class="status-tag">处理中</a-tag>
              <a-tag v-else-if="record.task_status === 'finished'" color="green" class="status-tag">已完成</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'check'">
              <a-button @click="checkThisTask(record)" type="primary" class="action-button view-button">
                <EyeOutlined />
                查看
              </a-button>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
              <a-button @click="showDeleteConfirm(record)" type="primary" danger class="action-button delete-button">
                <DeleteOutlined />
                删除
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <a-modal
      v-model:open="openCreateTask"
      title="创建新监测任务"
      @ok="handleOk"
      class="create-task-modal"
      cancelText="取消"
      okText="创建"
      :maskClosable="false"
    >
      <div class="modal-content">
        <div class="form-item">
          <label>选择模式:</label>
          <a-select
            v-model:value="selectMode"
            class="mode-select"
          >
            <a-select-option value="balanced">综合监测</a-select-option>
            <a-select-option value="efficient">快速监测</a-select-option>
            <a-select-option value="accurate">精确监测</a-select-option>
          </a-select>
        </div>
        <div class="form-item">
          <label>监测任务描述:</label>
          <a-textarea
            v-model:value="description"
            placeholder="请输入监测任务描述..."
            :auto-size="{ minRows: 3, maxRows: 6 }"
            class="description-textarea"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

  <script>
  import {
    PlusOutlined,
    HomeFilled,
    LogoutOutlined,
    ReloadOutlined,
    SearchOutlined,
    EyeOutlined,
    DeleteOutlined,
    FileOutlined,
    LoadingOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined
  } from '@ant-design/icons-vue';

  export default {
    name: 'TaskView',
    components: {
        PlusOutlined,
        HomeFilled,
        LogoutOutlined,
        ReloadOutlined,
        SearchOutlined,
        EyeOutlined,
        DeleteOutlined,
        FileOutlined,
        LoadingOutlined,
        CheckCircleOutlined,
        ClockCircleOutlined
    },
    data() {
      return {
        columns: [
        //   { title: 'ID', dataIndex: 'id', key: 'id' },
          { title: '创建时间', dataIndex: 'create_date', key: 'create_date', sorter: (a, b) => a.create_date.localeCompare(b.create_date) },
          { title: '更新时间', dataIndex: 'update_date', key: 'update_date', sorter: (a, b) => a.update_date.localeCompare(b.update_date) },
          { title: '模式', dataIndex: 'task_mode', key: 'task_mode' ,sorter: (a, b) => a.task_mode.localeCompare(b.task_mode)},
          { title: '状态', dataIndex: 'task_status', key: 'task_status'  ,sorter: (a, b) => a.task_status.localeCompare(b.task_status)},
          { title: '监测任务描述', dataIndex: 'task_description', key: 'task_description' },
          { title: '', dataIndex: 'check', key: 'check' },
          { title: '', dataIndex: 'operation', key: 'operation' }
        ],
        dataSource: [],
        openCreateTask: false,
        selectMode: 'balanced',
        description: '',
        search: '',
        viewMode: 'table', // 默认表格视图
      };
    },
    // 刚进入该路由时获取会话数据
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.getTasks();
      });
    },
    methods: {
      // 获取特定状态的会话数量
      getStatusCount(status) {
        return this.dataSource.filter(task => task.task_status === status).length;
      },

      getTasks() {
        this.$message.info('正在获取会话数据...'); // Show a message to indicate the data is being fetched
        // Assuming this.$axios is configured correctly for your backend API
        this.$axios.post('/getTaskByUser', { username: this.$store.state.username }).then((res) => {
          if (res.data.code === 200) {
            console.log('获取会话数据成功:', res.data.tasks);
            this.dataSource = res.data.tasks; // Adjust based on your actual API response structure
            console.log('获取会话数据成功:', this.dataSource);
          } else {
            console.error('获取会话数据失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('获取会话数据失败:', err);
        });
      },
        addTask() {
            this.openCreateTask = true;

        },
        handleOk() {
            this.$axios.post('/createTask', { username: this.$store.state.username, task_mode: this.selectMode, task_description: this.description }).then((res) => {
                if (res.data.code === 200) {
                    this.$message.success('创建会话成功',res.data.task_id);
                    this.getTasks();
                } else {
                    this.$message.error('创建会话失败:' + res.data.message);
                }
            }).catch((err) => {
                this.$message.error('创建会话失败:' + err);
            });
            this.selectMode = 'balanced';
            this.description = '';
            this.openCreateTask = false;
        },
        returnMain() {
            this.$router.push('/main');
        },
        logOut() {
            this.$router.push('/');
        },
        checkThisTask(record) {
            this.$message.info('查看会话详情:' + record.id);
            this.$axios.post('updateTaskTime', { task_id: record.id }).then((res) => {
                if (res.data.code === 200) {
                    console.log('更新会话时间成功:', res.data.task);
                    if(record.task_mode === 'balanced'){
                        this.$store.dispatch('updateTaskRecord', res.data.task);
                        this.$router.push('/task/balanced');
                    }
                } else {
                    console.error('更新会话时间失败:', res.data.message);
                }
            }).catch((err) => {
                console.error('更新会话时间失败:', err);
            });
        },
        showDeleteConfirm(record) {
            // 显示确认对话框
            this.$confirm({
                title: '确认删除',
                content: '确定要删除这个会话吗？此操作不可恢复。',
                okText: '确认',
                okType: 'danger',
                cancelText: '取消',
                onOk: () => {
                    // 显示加载提示
                    const hideLoading = this.$message.loading('正在删除会话...', 0);

                    // 发送删除请求
                    this.$axios.post('/deleteTask', {
                        task_id: record.id,
                        username: this.$store.state.username
                    }, {
                        timeout: 30000, // 增加超时时间到30秒
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    }).then((res) => {
                        // 关闭加载提示
                        hideLoading();

                        if (res.data.code === 200) {
                            this.$message.success('删除会话成功');
                            this.getTasks();
                        } else {
                            this.$message.error('删除会话失败: ' + res.data.message);
                        }
                    }).catch((err) => {
                        // 关闭加载提示
                        hideLoading();

                        console.error('删除会话失败:', err);
                        // 显示更友好的错误信息
                        this.$message.error('删除会话失败，请稍后重试');
                    });
                }
            });
        },
        searchTask() {
            this.$message.info('正在搜索会话...');
            if (this.search === '') {
                this.getTasks();
            } else {
                this.$axios.post('/getTaskByUser', { username: this.$store.state.username }).then((res) => {
                if (res.data.code === 200) {
                    let searchResult = res.data.tasks.filter((task) => {
                        return task.task_description.includes(this.search);
                    });
                    this.dataSource = searchResult; // Adjust based on your actual API response structure
                    console.log('获取会话数据成功:', this.dataSource);
                } else {
                    console.error('获取会话数据失败:', res.data.message);
                }
                }).catch((err) => {
                console.error('获取会话数据失败:', err);
                });


            }
        },
    },
  };
  </script>
  <style scoped>
.task-container {
  min-height: 100vh;
  padding: 20px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
  background: rgba(255, 255, 255, 0.85);
  padding: 25px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.task-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background-color: #1890ff;
  opacity: 0.8;
  z-index: 2;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 16px;
  color: #1890ff;
}

.page-title {
  font-size: 28px;
  font-weight: 800;
  margin: 0;
  color: #1890ff;
  text-shadow: 0 1px 2px rgba(24, 144, 255, 0.1);
  letter-spacing: -0.5px;
}

.header-right {
  display: flex;
  gap: 15px;
  align-items: center;
}

.search-box {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 250px;
  border-radius: 4px;
}

.search-button, .refresh-button, .add-button {
  display: flex;
  align-items: center;
  gap: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.search-button:hover, .refresh-button:hover, .add-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.logout-button {
  color: #ff4d4f;
  font-size: 18px;
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
}

/* 统计卡片样式 */
.stats-cards {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 200px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
  overflow: hidden;
}

.stat-card::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #1890ff;
  opacity: 0;
  transition: opacity 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-card:hover::after {
  opacity: 1;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 15px;
}

.total-icon {
  background-color: #e6f7ff;
  color: #1890ff;
}

.running-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.finished-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.preparing-icon {
  background-color: #e6f7ff;
  color: #1890ff;
}

.stat-info {
  flex: 1;
}

.stat-info h3 {
  font-size: 16px;
  color: #666;
  margin: 0 0 5px 0;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0;
}

/* 表格容器样式 */
.table-container {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  padding: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
  overflow: hidden;
}

.table-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%231890ff' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E");
  z-index: 0;
  opacity: 0.5;
  pointer-events: none;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 22px;
  font-weight: 700;
  color: #1890ff;
  margin: 0;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #1890ff;
  opacity: 0.7;
}

.task-table {
  width: 100%;
}

.task-row {
  transition: background-color 0.3s;
}

.task-row:hover {
  background-color: #f5f5f5;
}

/* 卡片视图样式 */
.task-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.task-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.task-card::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background-color: #1890ff;
  z-index: -1;
  border-radius: 14px;
  opacity: 0;
  transition: opacity 0.3s;
}

.task-card:hover::before {
  opacity: 0.5;
}

.task-card-header {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.task-card-title {
  margin-bottom: 10px;
}

.task-card-title h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.task-card-body {
  padding: 15px;
}

.task-card-info p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.task-card-info .anticon {
  margin-right: 8px;
}

.task-card-footer {
  padding: 15px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
}

.mode-tag, .status-tag {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.view-button {
  background-color: #1890ff;
}

.delete-button {
  background-color: #ff4d4f;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.create-task-modal {
  max-width: 500px;
}

.modal-content {
  padding: 10px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.mode-select {
  width: 100%;
  border-radius: 4px;
}

.description-textarea {
  width: 100%;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .task-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    flex-wrap: wrap;
  }

  .search-box {
    width: 100%;
    margin-bottom: 10px;
  }

  .search-input {
    flex: 1;
  }
}
</style>