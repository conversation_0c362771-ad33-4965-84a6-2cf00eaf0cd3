<template>
  <div class="task-container">
    <div class="task-header">
      <div class="header-left fade-in">
        <a-button @click="returnMain" type="text" class="nav-button">
          <HomeFilled />
          返回首页
        </a-button>
        <h1 class="page-title">会话管理</h1>
      </div>
      <div class="header-right fade-in">
        <div class="search-box">
          <a-input
            placeholder="搜索会话描述..."
            v-model:value="search"
            class="search-input"
          >
            <template #prefix><SearchOutlined /></template>
          </a-input>
          <a-button type="primary" @click="searchTask" class="search-button">
            搜索
          </a-button>
        </div>
        <a-button type="primary" @click="getTasks" class="refresh-button">
          <ReloadOutlined />
          刷新
        </a-button>
        <a-button type="primary" @click="addTask" class="add-button">
          <PlusOutlined />
          新建会话
        </a-button>
        <a-button @click="logOut" type="text" class="logout-button">
          <LogoutOutlined />
        </a-button>
      </div>
    </div>

    <div class="task-content slide-in-up">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <div class="stat-card fade-in">
          <div class="stat-icon total-icon">
            <FileOutlined />
          </div>
          <div class="stat-info">
            <h3>总会话数</h3>
            <p class="stat-value">{{ dataSource.length }}</p>
          </div>
        </div>

        <div class="stat-card fade-in delay-1">
          <div class="stat-icon running-icon">
            <LoadingOutlined />
          </div>
          <div class="stat-info">
            <h3>处理中</h3>
            <p class="stat-value">{{ getStatusCount('running') }}</p>
          </div>
        </div>

        <div class="stat-card fade-in delay-2">
          <div class="stat-icon finished-icon">
            <CheckCircleOutlined />
          </div>
          <div class="stat-info">
            <h3>已完成</h3>
            <p class="stat-value">{{ getStatusCount('finished') }}</p>
          </div>
        </div>

        <div class="stat-card fade-in delay-3">
          <div class="stat-icon preparing-icon">
            <ClockCircleOutlined />
          </div>
          <div class="stat-info">
            <h3>待处理</h3>
            <p class="stat-value">{{ getStatusCount('preparing') }}</p>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <div class="table-container">
        <div class="table-header">
          <h2 class="section-title">会话列表</h2>
          <div class="view-toggle">
            <a-button-group>
              <a-button type="primary" :ghost="viewMode !== 'table'" @click="viewMode = 'table'">
                <TableOutlined />
                表格视图
              </a-button>
              <a-button type="primary" :ghost="viewMode !== 'card'" @click="viewMode = 'card'">
                <AppstoreOutlined />
                卡片视图
              </a-button>
            </a-button-group>
          </div>
        </div>

        <!-- 表格视图 -->
        <a-table
          v-if="viewMode === 'table'"
          :dataSource="dataSource"
          :columns="columns"
          :pagination="{
            showSizeChanger: false,
            defaultPageSize: 8,
            showTotal: total => `共 ${total} 条记录`
          }"
          class="task-table"
          :rowClassName="() => 'task-row'"
        >
          <template v-slot:bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'task_mode'">
              <a-tag v-if="record.task_mode === 'balanced'" color="blue" class="mode-tag">平衡模式</a-tag>
              <a-tag v-else-if="record.task_mode === 'efficient'" color="green" class="mode-tag">高效模式</a-tag>
              <a-tag v-else-if="record.task_mode === 'accurate'" color="red" class="mode-tag">精确模式</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'task_status'">
              <a-tag v-if="record.task_status === 'preparing'" color="blue" class="status-tag">待处理</a-tag>
              <a-tag v-else-if="record.task_status === 'running'" color="orange" class="status-tag">处理中</a-tag>
              <a-tag v-else-if="record.task_status === 'finished'" color="green" class="status-tag">已完成</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'check'">
              <a-button @click="checkThisTask(record)" type="primary" class="action-button view-button">
                <EyeOutlined />
                查看
              </a-button>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
              <a-button @click="showDeleteConfirm(record)" type="primary" danger class="action-button delete-button">
                <DeleteOutlined />
                删除
              </a-button>
            </template>
          </template>
        </a-table>

        <!-- 卡片视图 -->
        <div v-else class="task-cards">
          <div v-for="record in dataSource" :key="record.id" class="task-card hover-lift">
            <div class="task-card-header">
              <div class="task-card-title">
                <h3>{{ record.task_description || '无描述' }}</h3>
                <a-tag v-if="record.task_mode === 'balanced'" color="blue" class="mode-tag">平衡模式</a-tag>
                <a-tag v-else-if="record.task_mode === 'efficient'" color="green" class="mode-tag">高效模式</a-tag>
                <a-tag v-else-if="record.task_mode === 'accurate'" color="red" class="mode-tag">精确模式</a-tag>
              </div>
              <div class="task-card-status">
                <a-tag v-if="record.task_status === 'preparing'" color="blue" class="status-tag">待处理</a-tag>
                <a-tag v-else-if="record.task_status === 'running'" color="orange" class="status-tag">处理中</a-tag>
                <a-tag v-else-if="record.task_status === 'finished'" color="green" class="status-tag">已完成</a-tag>
              </div>
            </div>

            <div class="task-card-body">
              <div class="task-card-info">
                <p><ClockCircleOutlined /> 创建时间: {{ record.create_date }}</p>
                <p><SyncOutlined /> 更新时间: {{ record.update_date }}</p>
              </div>
            </div>

            <div class="task-card-footer">
              <a-button @click="checkThisTask(record)" type="primary" class="action-button view-button">
                <EyeOutlined />
                查看
              </a-button>
              <a-button @click="showDeleteConfirm(record)" type="primary" danger class="action-button delete-button">
                <DeleteOutlined />
                删除
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <a-modal
      v-model:open="openCreateTask"
      title="创建新会话"
      @ok="handleOk"
      class="create-task-modal"
      cancelText="取消"
      okText="创建"
      :maskClosable="false"
    >
      <div class="modal-content">
        <div class="form-item">
          <label>选择模式:</label>
          <a-select
            v-model:value="selectMode"
            class="mode-select"
          >
            <a-select-option value="balanced">平衡模式</a-select-option>
            <a-select-option value="efficient">高效模式</a-select-option>
            <a-select-option value="accurate">精确模式</a-select-option>
          </a-select>
        </div>
        <div class="form-item">
          <label>会话描述:</label>
          <a-textarea
            v-model:value="description"
            placeholder="请输入会话描述..."
            :auto-size="{ minRows: 3, maxRows: 6 }"
            class="description-textarea"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

  <script>
  import {
    PlusOutlined,
    HomeFilled,
    LogoutOutlined,
    ReloadOutlined,
    SearchOutlined,
    EyeOutlined,
    DeleteOutlined,
    FileOutlined,
    LoadingOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    TableOutlined,
    AppstoreOutlined,
    SyncOutlined
  } from '@ant-design/icons-vue';

  export default {
    name: 'TaskView',
    components: {
        PlusOutlined,
        HomeFilled,
        LogoutOutlined,
        ReloadOutlined,
        SearchOutlined,
        EyeOutlined,
        DeleteOutlined
    },
    data() {
      return {
        columns: [
        //   { title: 'ID', dataIndex: 'id', key: 'id' },
          { title: '创建时间', dataIndex: 'create_date', key: 'create_date', sorter: (a, b) => a.create_date.localeCompare(b.create_date) },
          { title: '更新时间', dataIndex: 'update_date', key: 'update_date', sorter: (a, b) => a.update_date.localeCompare(b.update_date) },
          { title: '模式', dataIndex: 'task_mode', key: 'task_mode' ,sorter: (a, b) => a.task_mode.localeCompare(b.task_mode)},
          { title: '状态', dataIndex: 'task_status', key: 'task_status'  ,sorter: (a, b) => a.task_status.localeCompare(b.task_status)},
          { title: '会话描述', dataIndex: 'task_description', key: 'task_description' },
          { title: '', dataIndex: 'check', key: 'check' },
          { title: '', dataIndex: 'operation', key: 'operation' }
        ],
        dataSource: [],
        openCreateTask: false,
        selectMode: 'balanced',
        description: '',
        search: '',
      };
    },
    // 刚进入该路由时获取会话数据
    beforeRouteEnter(to, from, next) {
      next((vm) => {
        vm.getTasks();
      });
    },
    methods: {
      getTasks() {
        this.$message.info('正在获取会话数据...'); // Show a message to indicate the data is being fetched
        // Assuming this.$axios is configured correctly for your backend API
        this.$axios.post('/getTaskByUser', { username: this.$store.state.username }).then((res) => {
          if (res.data.code === 200) {
            console.log('获取会话数据成功:', res.data.tasks);
            this.dataSource = res.data.tasks; // Adjust based on your actual API response structure
            console.log('获取会话数据成功:', this.dataSource);
          } else {
            console.error('获取会话数据失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('获取会话数据失败:', err);
        });
      },
        addTask() {
            this.openCreateTask = true;

        },
        handleOk() {
            this.$axios.post('/createTask', { username: this.$store.state.username, task_mode: this.selectMode, task_description: this.description }).then((res) => {
                if (res.data.code === 200) {
                    this.$message.success('创建会话成功',res.data.task_id);
                    this.getTasks();
                } else {
                    this.$message.error('创建会话失败:' + res.data.message);
                }
            }).catch((err) => {
                this.$message.error('创建会话失败:' + err);
            });
            this.selectMode = 'balanced';
            this.description = '';
            this.openCreateTask = false;
        },
        returnMain() {
            this.$router.push('/main');
        },
        logOut() {
            this.$router.push('/');
        },
        checkThisTask(record) {
            this.$message.info('查看会话详情:' + record.id);
            this.$axios.post('updateTaskTime', { task_id: record.id }).then((res) => {
                if (res.data.code === 200) {
                    console.log('更新会话时间成功:', res.data.task);
                    if(record.task_mode === 'balanced'){
                        this.$store.dispatch('updateTaskRecord', res.data.task);
                        this.$router.push('/task/balanced');
                    }
                } else {
                    console.error('更新会话时间失败:', res.data.message);
                }
            }).catch((err) => {
                console.error('更新会话时间失败:', err);
            });
        },
        showDeleteConfirm(record) {
            this.$axios.post('/deleteTask', { task_id: record.id ,username:this.$store.state.username}).then((res) => {
                if (res.data.code === 200) {
                    this.$message.success('删除会话成功');
                    this.getTasks();
                } else {
                    this.$message.error('删除会话失败:' + res.data.message);
                }
            }).catch((err) => {
                this.$message.error('删除会话失败:' + err);
            });
        },
        searchTask() {
            this.$message.info('正在搜索会话...');
            if (this.search === '') {
                this.getTasks();
            } else {
                this.$axios.post('/getTaskByUser', { username: this.$store.state.username }).then((res) => {
                if (res.data.code === 200) {
                    let searchResult = res.data.tasks.filter((task) => {
                        return task.task_description.includes(this.search);
                    });
                    this.dataSource = searchResult; // Adjust based on your actual API response structure
                    console.log('获取会话数据成功:', this.dataSource);
                } else {
                    console.error('获取会话数据失败:', res.data.message);
                }
                }).catch((err) => {
                console.error('获取会话数据失败:', err);
                });


            }
        },
    },
  };
  </script>
  <style scoped>
.task-container {
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 16px;
  color: #1890ff;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.header-right {
  display: flex;
  gap: 15px;
  align-items: center;
}

.search-box {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 250px;
  border-radius: 4px;
}

.search-button, .refresh-button, .add-button {
  display: flex;
  align-items: center;
  gap: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.search-button:hover, .refresh-button:hover, .add-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.logout-button {
  color: #ff4d4f;
  font-size: 18px;
}

.task-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  flex: 1;
}

.task-table {
  width: 100%;
}

.task-row {
  transition: background-color 0.3s;
}

.task-row:hover {
  background-color: #f5f5f5;
}

.mode-tag, .status-tag {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 5px;
  border-radius: 4px;
  transition: all 0.3s;
}

.view-button {
  background-color: #1890ff;
}

.delete-button {
  background-color: #ff4d4f;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.create-task-modal {
  max-width: 500px;
}

.modal-content {
  padding: 10px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.mode-select {
  width: 100%;
  border-radius: 4px;
}

.description-textarea {
  width: 100%;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .task-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    flex-wrap: wrap;
  }

  .search-box {
    width: 100%;
    margin-bottom: 10px;
  }

  .search-input {
    flex: 1;
  }
}
</style>