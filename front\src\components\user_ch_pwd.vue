<template>
  <div class="user-change-password-container">
    <!-- 验证码输入行 -->
    <a-space direction="horizontal" class="input-container">
      <a-input class="ant-input-certify" v-model:value="verificationCode" placeholder="输入验证码" />
      <a-button class="ant-input-sendc"  @click="sendVerificationCode">发送邮箱验证码</a-button>
    </a-space>

    <!-- 新密码输入行 -->
    <a-input-password class="ant-input-certify" v-model:value="newPassword" type="password" placeholder="输入新密码" />

    <!-- 完成修改密码按钮 -->
    <a-button class="ucpwd-fi" type="primary" @click="changePassword">修改密码</a-button>
  </div>
</template>

<script>
export default {
  name: 'UserChangePassword',
  data() {
    return {
      verificationCode: '',
      newPassword: '',
      trueCode: ''
    };
  },
  methods: {
    sendVerificationCode() {
      // TODO: 实现发送验证码的逻辑
      console.log('发送验证码:', this.verificationCode);
      this.$axios.post('/sendVerificationCode', {
          email: this.$store.state.email
        }).then((res) => {
          if (res.data.code === 200) {
            this.trueCode=res.data.verificationCode;
            this.$message.success('验证码发送成功');
          } else {
            console.error('验证码发送失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('验证码发送失败:', err);
        });
    },
    changePassword() {
      // TODO: 实现修改密码的逻辑
      if(this.verificationCode === this.trueCode){
        this.$axios.post('/changePassword', {
          password: this.newPassword,
          username: this.$store.state.username
        }).then((res) => {
          if (res.data.code === 200) {
            this.$message.success('密码修改成功');
            this.$store.dispatch('updatePassword', this.newPassword);
          } else {
            console.error('密码修改失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('密码修改失败:', err);
        });
      }
      else{
        this.$message.error('验证码错误');
      }
    },
  },
};
</script>

<style scoped>
.user-change-password-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-direction: column;
}

.input-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 24vw; /* 让这一行充满整个容器宽度 */
  margin-bottom: 10px; /* 调整间距根据需要 */
}
.user-change-password-container > .ant-input-password {
  width: 24vw; /* 让输入框宽度与验证码输入行一致 */
}

.user-change-password-container > * {
  margin: 20px;
}

.ant-input-sendc{
  /* font-size: 20px;
  height:40px; */
  border: 1px solid #2f2f2f;
}
.ant-input-certify {
  font-size: 20px;
}
.ucpwd-fi{
  margin-top: 20px;
  font-size: 20px;
  height: 60px;
}
</style>
