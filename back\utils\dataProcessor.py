import json
import os
import base64
def read_config(jsonPath):
    try:
        with open(jsonPath, 'r',encoding='utf-8') as config_file:
            config_data = json.load(config_file)
        return config_data
    except FileNotFoundError:
        return {"error": "Config file not found"}
    except json.JSONDecodeError:
        return {"error": "Invalid JSON in config file"}

def write_config(config_data,jsonPath):
    def convert_to_numbers(data):
        for key, value in data.items():
            if isinstance(value, dict):
                # 递归处理嵌套的字典
                data[key] = convert_to_numbers(value)
            elif isinstance(value, str):
                # 尝试将字符串类型的数字转换为整数或浮点数
                if value.isdigit():
                    data[key] = int(value)
                else:
                    try:
                        data[key] = float(value)
                    except ValueError:
                        pass  # 如果无法转换为数字，则保持原样
        return data

    try:
        # 将配置数据中的字符串类型转换为数字类型
        converted_config = convert_to_numbers(config_data)

        with open(jsonPath, 'w',encoding='utf-8') as config_file:
            json.dump(converted_config, config_file, indent=2)
        return {"success": True}
    except Exception as e:
        return {"error": str(e)}
    

def process_directory(directory):
    data = {}

    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.isfile(filepath):
            timestamp, similarity = parse_filename(filename)
            similarity = float('.'.join(similarity.split('.')[:-1]))

            # 读取文件内容
            with open(filepath, 'rb') as file:
                file_content = file.read()

            # 将二进制数据转换为 base64 编码的字符串
            encoded_content = base64.b64encode(file_content).decode('utf-8')

            # 确定文件格式
            file_format = get_file_format(filename)
            # 构造数据
            data[timestamp] = {
                'similarity': similarity,
                'content': encoded_content,
                'format': file_format
            }

    return data

def parse_filename(filename):
    # 假设文件名格式为 timestamp_similarity.xxx
    timestamp, similarity = filename.split('_')[:2]
    return timestamp, similarity

def get_file_format(filename):
    # 获取文件后缀，假设文件名格式为 timestamp_similarity.xxx
    _, _, file_extension = filename.rpartition('.')
    return file_extension