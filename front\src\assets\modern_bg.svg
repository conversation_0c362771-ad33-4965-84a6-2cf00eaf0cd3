<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f0f7ff" />
      <stop offset="100%" stop-color="#e6f7ff" />
    </linearGradient>
    
    <!-- 城市轮廓渐变 -->
    <linearGradient id="cityGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#1890ff" stop-opacity="0.05" />
      <stop offset="100%" stop-color="#1890ff" stop-opacity="0.15" />
    </linearGradient>
    
    <!-- 装饰元素渐变 -->
    <linearGradient id="decorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1890ff" stop-opacity="0.05" />
      <stop offset="100%" stop-color="#1890ff" stop-opacity="0.1" />
    </linearGradient>
    
    <!-- 波浪图案 -->
    <pattern id="wavePattern" patternUnits="userSpaceOnUse" width="200" height="200" patternTransform="scale(0.5) rotate(0)">
      <path d="M0,100 C40,80 60,120 100,100 C140,80 160,120 200,100 C240,80 260,120 300,100 C340,80 360,120 400,100" 
            fill="none" stroke="#1890ff" stroke-width="1" stroke-opacity="0.1" />
      <path d="M0,150 C40,130 60,170 100,150 C140,130 160,170 200,150 C240,130 260,170 300,150 C340,130 360,170 400,150" 
            fill="none" stroke="#1890ff" stroke-width="1" stroke-opacity="0.1" />
      <path d="M0,50 C40,30 60,70 100,50 C140,30 160,70 200,50 C240,30 260,70 300,50 C340,30 360,70 400,50" 
            fill="none" stroke="#1890ff" stroke-width="1" stroke-opacity="0.1" />
    </pattern>
  </defs>
  
  <!-- 主背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)" />
  
  <!-- 波浪图案背景 -->
  <rect width="1920" height="1080" fill="url(#wavePattern)" />
  
  <!-- 左上角装饰 -->
  <circle cx="100" cy="100" r="300" fill="url(#decorGradient)" />
  
  <!-- 右下角装饰 -->
  <circle cx="1820" cy="980" r="400" fill="url(#decorGradient)" />
  
  <!-- 城市轮廓 - 底部 -->
  <path d="M0,800 L50,800 L50,780 L100,780 L100,820 L150,820 L150,760 L200,760 L200,800 L250,800 L250,750 L300,750 L300,790 
           L350,790 L350,740 L400,740 L400,770 L450,770 L450,730 L500,730 L500,780 L550,780 L550,720 L600,720 L600,760 
           L650,760 L650,710 L700,710 L700,750 L750,750 L750,700 L800,700 L800,740 L850,740 L850,690 L900,690 L900,730 
           L950,730 L950,680 L1000,680 L1000,720 L1050,720 L1050,670 L1100,670 L1100,710 L1150,710 L1150,660 L1200,660 
           L1200,700 L1250,700 L1250,650 L1300,650 L1300,690 L1350,690 L1350,640 L1400,640 L1400,680 L1450,680 L1450,630 
           L1500,630 L1500,670 L1550,670 L1550,620 L1600,620 L1600,660 L1650,660 L1650,610 L1700,610 L1700,650 L1750,650 
           L1750,600 L1800,600 L1800,640 L1850,640 L1850,590 L1900,590 L1900,630 L1920,630 L1920,1080 L0,1080 Z" 
        fill="url(#cityGradient)" />
  
  <!-- 抽象数据连接线 -->
  <g stroke="#1890ff" stroke-opacity="0.1" stroke-width="1">
    <line x1="200" y1="200" x2="400" y2="300" />
    <line x1="400" y1="300" x2="600" y2="250" />
    <line x1="600" y1="250" x2="800" y2="350" />
    <line x1="800" y1="350" x2="1000" y2="300" />
    <line x1="1000" y1="300" x2="1200" y2="400" />
    <line x1="1200" y1="400" x2="1400" y2="350" />
    <line x1="1400" y1="350" x2="1600" y2="450" />
    <line x1="1600" y1="450" x2="1800" y2="400" />
  </g>
  
  <!-- 抽象数据节点 -->
  <g fill="#1890ff" fill-opacity="0.2">
    <circle cx="200" cy="200" r="5" />
    <circle cx="400" cy="300" r="5" />
    <circle cx="600" cy="250" r="5" />
    <circle cx="800" cy="350" r="5" />
    <circle cx="1000" cy="300" r="5" />
    <circle cx="1200" cy="400" r="5" />
    <circle cx="1400" cy="350" r="5" />
    <circle cx="1600" cy="450" r="5" />
    <circle cx="1800" cy="400" r="5" />
  </g>
</svg>
