<template>
    <div>
      <a-table :dataSource="dataSource" :columns="columns" :pagination="{showSizeChanger: false, defaultPageSize: 6 }" style="width: 70vw;margin: 0 auto;">
        <template v-slot:bodyCell="{ column, record}">
          <template v-if="column.dataIndex === 'operation'">
            <a-button type="primary" @click="showDeleteConfirm(record.username)" :disabled="record.username === 'admin'" danger>删除用户</a-button>
          </template>
  
          <template v-else-if="column.dataIndex === 'is_admin'">
            <a-switch v-model:checked="record[column.dataIndex]" checked-children="是" un-checked-children="否" @change="handleSwitchChange(record)" :disabled="record.username === 'admin'" />
          </template>
  
          <template v-else-if="column.editable && record.username !== 'admin'">
            <a-input v-model:value="record[column.dataIndex]" @input="handleInput(record)"/>
          </template>
          
          <template v-else>
            {{ record[column.dataIndex] }}
          </template>
        </template>
      </a-table>
    </div>
  </template>
  
  <script>
  export default {
    data() {
      return {
        columns: [
          { title: '用户名', dataIndex: 'username', key: 'username', editable: false },
          { title: '密码', dataIndex: 'password', key: 'password', editable: true },
          { title: '邮箱', dataIndex: 'email', key: 'email', editable: true },
          { title: '是否为管理员', dataIndex: 'is_admin', key: 'is_admin', editable: true },
          { title: '', dataIndex: 'operation', key: 'operation' }
        ],
        dataSource: [],
        dataChanged: false // 新增标志，表示数据是否发生变化
      };
    },
    created() {
      this.fetchData();
    },
    watch: {
    // 监听数据变化，触发更新逻辑
    dataSource: {
      handler() {
        this.updateData();
      },
      deep: true
    }
  },
    methods: {
      fetchData() {
        // 使用axios发送请求获取用户数据
        this.$axios.get('/getAllUsers').then((res) => {
          if (res.data.code === 200) {
            this.dataSource = res.data.users;
            console.log('获取用户数据成功:', this.dataSource);
          } else {
            console.error('获取用户数据失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('获取用户数据失败:', err);
        });
      },
      showDeleteConfirm(username) {
        console.log('删除用户:', username);
        this.$axios.post('/deleteUser', { username: username}).then((res) => {
          if (res.data.code === 200) {
            console.log('删除用户成功:', res.data.message);
            this.fetchData();
          } else {
            console.error('删除用户失败:', res.data.message);
          }
        }).catch((err) => {
          console.error('删除用户失败:', err);
        });
      },
      handleSwitchChange(record) {
      // 处理开关切换的逻辑省略...
      console.log('开关状态发生变化:', record);
      this.dataChanged = true; // 开关状态发生变化，设置标志
    },
    handleInput(record) {
      // 处理输入框变化的逻辑省略...
      console.log('输入框内容发生变化:', record);
      this.dataChanged = true; // 输入框内容发生变化，设置标志
    }, 
      updateData() {
      if (this.dataChanged) {
        // 发送数据更新请求
        this.$axios.post('/updateUsers', { users: this.dataSource }).then((res) => {
          if (res.data.code === 200) {
            console.log('更新用户数据成功');
          } else {
            console.error('更新用户数据失败');
          }
        }).catch((err) => {
          console.error('更新用户数据失败:', err);
        });
        this.dataChanged = false; // 重置标志
      }
    }
    }
  };
  </script>
  