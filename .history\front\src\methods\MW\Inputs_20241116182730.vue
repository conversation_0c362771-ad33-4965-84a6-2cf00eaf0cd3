<template>
    <div class="inputs-box">
        <div class="video-play">
            <video class="video" id="videoPlayer" ref="videoPlayer" controls>
            </video>
            <input type="file" ref="fileInput" style="display: none" @change="handleFileChange">
        </div>
        <div class="inputs">
            <div class="buttons">
                <!-- <a-button type="dashed" @click="scan" ghost unabled>预先采样</a-button> -->
                <a-button type="dashed" @click="triggerFileInput" ghost>上传视频</a-button>
                <DoubleRightOutlined style="color: aliceblue;"/>
                <a-button type="dashed" @click="updateSettings" ghost>上传参数</a-button>
                <DoubleRightOutlined style="color: aliceblue;"/>
                <a-button type="dashed" @click="scan" ghost>生成分析</a-button>
                <span class="top-left"></span>
                <span class="top-right"></span>
                <span class="bottom-left"></span>
                <span class="bottom-right"></span>
            </div>
            <div class="settings">
                <div class="settingsBB">
                    <div class="settingsHeader">
                            <SettingFilled style="color: aliceblue;font-size: 22px;"/>
                            <p>参数设定</p>
                        </div>
                    <div class="settingsBox">
                        
                        <div id="prompt-box">
                            <textarea name="prompt" rows="3" placeholder="请输入提示 ... ... " v-model="$store.state.task_record.task_config.prompt"></textarea>
                        </div>

                        <div class="wrap-content MAC">
                            <div class="content">
                                <label class="form-label">最大并发数量</label>
                                <input type="text" name="MAC" class="form-input" v-model="$store.state.task_record.task_config.MAC">
                            </div>
                            <!-- 描述文字 -->
                            <p class="form-label">关于参数:<br/>&nbsp;&nbsp;&nbsp;&nbsp;通过该参数设置最大可用的并行数量，该参数受限于机器配置，但系统将会酌情考虑，尽可能使用设定的最大并发数量.<br/>&nbsp;&nbsp;&nbsp;&nbsp;理论上，设定的并发数越多，系统推理速度越快。</p>
                        </div>

                        <div class="wrap-content NCS">
                            <div class="content">
                                <label class="form-label">分段数量</label>
                                <input type="text" name="NCS" class="form-input" v-model="$store.state.task_record.task_config.NCS">
                            </div>
                            <!-- 描述文字 -->
                            <p class="form-label">关于参数:<br/>&nbsp;&nbsp;&nbsp;&nbsp;设置视频分段处理的数量。 <br/>&nbsp;&nbsp;&nbsp;&nbsp;理论上，分段数量越多，视频处理越快，但不可分得太细，可能会引起反效果。</p>
                        </div>
                        <div class="wrap-content TT">
                            <div class="content">
                                <label class="form-label">相似度终止阈值</label>
                                <input type="text" name="TT" class="form-input" v-model="$store.state.task_record.task_config.RW.TT">
                            </div>
                            <!-- 描述文字 -->
                            <p class="form-label">关于参数:<br/>&nbsp;&nbsp;&nbsp;&nbsp;设定终止的最小相似度阈值。<br/>&nbsp;&nbsp;&nbsp;&nbsp;理论上，终止相似度阈值越低，也就代表着对相似度的要求越低，推理速度越快，但相应准确度可能有欠缺。</p>
                        </div>

                        <div class="wrap-content TI">
                            <div class="content">
                                <label class="form-label">终止时间间隔</label>
                                <input type="text" name="TI" class="form-input" v-model="$store.state.task_record.task_config.RW.TI">
                            </div>
                            <!-- 描述文字 -->
                            <p class="form-label">关于参数:<br/>&nbsp;&nbsp;&nbsp;&nbsp;设定终止的最小时间间隔。<br/>&nbsp;&nbsp;&nbsp;&nbsp;理论上，设定较小的终止时间间隔，也就代表着对视频的粒度要求越细致，一般会导致较高的推理代价，但准确度会相应提高。</p>
                        </div>


                        <div class="wrap-content NCM">
                            <div class="content">
                                <label class="form-label">候选片段数量</label>
                                <input type="text" name="NCM" class="form-input" v-model="$store.state.task_record.task_config.NCM">
                            </div>
                            <!-- 描述文字 -->
                            <p class="form-label">关于参数:<br/>&nbsp;&nbsp;&nbsp;&nbsp;设定期望生成的片段数量。 <br/>&nbsp;&nbsp;&nbsp;&nbsp;理论上，要求生成的片段数量越多，时间代价越高。</p>
                        </div>
                    </div>
                
                </div>
                
            </div>  
        </div>
    </div>
</template>
  
<script>
import {DoubleRightOutlined,SettingFilled} from '@ant-design/icons-vue';
export default {
    name: 'InputsView',
    components: {
        DoubleRightOutlined,
        SettingFilled
    },
    props: {
        setTs: Boolean,
        atTs: Number,
        setTsFunc: Function
    },
    data() {
        return {
        };
    },
    // 创建组件时调用
    created() {
        this.$store.dispatch('updateTaskReady', true);
        this.downloadVideo();
    },
    
    beforeRouteEnter() {
        console.log('beforeRouteEnter');
       
    },
    watch: {
        setTs: function() {
            this.handleSetTs();
        }
    },
    methods: {
        handleSetTs() {
            if (this.setTs) {
                this.$refs.videoPlayer.currentTime = this.atTs;
                this.setTsFunc(false);
            }
        },
        // 下载视频
        downloadVideo() {
            this.$axios.get('/DownloadVideo', { params: { task_id: this.$store.state.task_record.id } }).then((res) => {
                console.log('下载视频成功:', res);
                URL.revokeObjectURL(this.$refs.videoPlayer.src);
                this.$refs.videoPlayer.src = res.request.responseURL;
            }).catch((err) => {
                console.error('下载视频失败:', err);
            }).finally(() => {
                this.$store.dispatch('updateTaskReady', false);
            });
            
        },

        // 上传视频
        triggerFileInput() {
            // 触发文件输入框点击事件
            this.$refs.fileInput.click();
        },
        handleFileChange(event) {
            // 处理文件选择变化事件
            const file = event.target.files[0];
            if (file) {
                // 通过URL.createObjectURL创建视频源
                this.videoSource = URL.createObjectURL(file);
                // 更新视频播放器的源
                this.$refs.videoPlayer.src = this.videoSource;

                // 发送视频文件到服务器
                this.uploadVideo(file);
            }
        },
        convertBase64ToBlob(base64Data, videoFormat) {
            console.log('base64Data:', base64Data);
            // 移除base64数据前缀
            const base64WithoutPrefix = base64Data.replace(/^data:video\/\w+;base64,/, '');

            // 将base64字符串解码为二进制数据
            const decodedContent = atob(base64WithoutPrefix);

            // 创建一个Uint8Array以存储二进制数据
            const arrayBuffer = new ArrayBuffer(decodedContent.length);
            const uint8Array = new Uint8Array(arrayBuffer);

            // 将解码后的二进制数据填充到Uint8Array中
            for (let i = 0; i < decodedContent.length; i++) {
            uint8Array[i] = decodedContent.charCodeAt(i);
        }

        // 创建Blob对象
        const blob = new Blob([uint8Array], { type: `video/${videoFormat}` });

        return blob;
      },
        async uploadVideo(file) {
            this.uploading = true; // 设置上传状态为true
            this.$store.dispatch('updateUploading', true);

            const formData = new FormData();
            formData.append('video', file);
            formData.append('task_id', this.$store.state.task_record.id);
            // 发送 POST 请求到服务器的/InputVideo接口
            this.$axios.post('/InputVideo', formData,{
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            }).then(response => {
                this.$store.dispatch('updateTaskRecord', response.data.task);
                console.log(response.data);
            }).catch(error => {
                console.error('Error uploading video:', error);
            }).finally(() => {
                this.$store.dispatch('updateUploading', false);
                 // 无论上传成功或失败，都将上传状态设置为false
            });

        },
        // 上传配置
        updateSettings() {
            this.$axios.post('InputSettings', { task_id: this.$store.state.task_record.id,task_config:this.$store.state.task_record.task_config}).then((res) => {
                if (res.data.code === 200) {
                    console.log('更新会话时间成功:', res.data.task);
                    this.$store.dispatch('updateTaskRecord', res.data.task);
                } else {
                    console.error('更新会话时间失败:', res.data.message);
                }
            }).catch((err) => {
                console.error('更新会话时间失败:', err);
            });
        },
        scan() {
            if(this.$store.state.task_record.task_mode=='balanced'){
                this.$axios.post('/RWScan', { task_id: this.$store.state.task_record.id}).then((res) => {
                    // this.$message.success('分析成功');
                    console.log('分析成功:', res);
                    this.$axios.post('/taskDetail', { task_id: this.$store.state.task_record.id }).then((res) => {
                        this.$store.dispatch('updateTaskRecord', res.data.task);
                    }).catch((err) => {
                        console.error('获取任务详情失败:', err);
                    });
                    this.shouldRender = false;
                    this.$router.push({ path: '/task' });
                }).catch((err) => {
                    console.error('分析失败:', err);
                });
            }else{
                this.$message.error('模式开发中');
            }
        },
    },
};  
</script>
  
<style scoped>
.video-play {
  height: 40vh;
  width: 40vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center; 
border-bottom: 3px solid #FFFEFC;
}

#videoPlayer {
  height: 30vh;
  width: 30vw;
}

.inputs {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 50vh;
  background-color: #2C3D50;
}
.buttons {
  display: flex;
  flex-direction: row;
  /* 均分 */
    justify-content: space-around;
    align-items: center;
  height: 10vh;
  width: 33vw;
  background-color: #2C3D50;
  position: relative;
  border: 1px dotted #FFFEFC;
  margin-top: 40px;
  margin-bottom: 40px;
}
.top-left{
    position: absolute;
    left: -3px;
    top: -3px;
    padding: 10px;
    border-style: solid;
    border-color: #00FFFE;
    border-width: 5px 0 0 5px;
}
.top-right{
    position: absolute;
    right: -3px;
    top: -3px;
    padding: 10px;
    border-style: solid;
    border-color: #00FFFE;
    border-width: 5px 5px 0 0;
}
.bottom-right{
    position: absolute;
    right: -3px;
    bottom: -3px;
    padding: 10px;
    border-style: solid;
    border-color: #00FFFE;
    border-width: 0 5px 5px 0;
}
.bottom-left{
    position: absolute;
    left: -3px;
    bottom: -3px;
    padding: 10px;
    border-style: solid;
    border-color: #00FFFE;
    border-width: 0 0 5px 5px;
}
.settings {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 30vh;
  width: 40vw;
  background-color: #2C3D50;
    border-top: 3px solid #FFFEFC;   
padding-top: 40px;
padding-bottom: 40px;
}

.settingsBB{
    width: 30vw;
  height: 22vh;
  padding: 20px;
    border: 1px dotted #FFFEFC;
    display: flex;
    flex-direction: column;
}
.settingsHeader{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 10%;
    background-color: #2C3D50;
    margin-bottom: 20px;
}
.settingsHeader p{
    margin-left: 10px;
    font-size: 20px;
    font-weight: bold;
    color: #FFFEFC;
}
.settingsBox {

  /* display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center; */
  background-color: #2C3D50;
  
  overflow-y: scroll;
  color: #FFFEFC;
  border: 1px dotted #FFFEFC;
}
/* 定制滚动条样式 */
.settingsBox::-webkit-scrollbar {
      width: 0px; /* 滚动条宽度 */
    }

.settingsBox::-webkit-scrollbar-thumb {
  background-color: #1C2733; /* 滚动条滑块颜色 */
}

.settingsBox::-webkit-scrollbar-track {
  background-color: #FFFEFC; /* 滚动条轨道颜色 */
}

.wrap-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 88%;
  margin: 6%;
  justify-content: center;
  align-items: center;
  background-color: #057BBD;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content label{
  margin-top: 50px;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: bold;
  color: #021E43;
}

.content input{
  font-size: 20px;
  width: 60%;
  text-align: center;
}

.wrap-content p{
  white-space: pre-line;
  display: block;
  margin: 30px;
  font-size: 12px;
  font-weight: bold;
  color: #021E43;
  /* 不要居中，正常显示段落 */
    text-align: left;
}

#prompt-box {
  display: flex;
  flex-direction: row;
}

textarea {
  margin: 0 auto;
  width: 88%;
  border: 0px;
  margin-top: 30px;
  padding: 0px;
  font-size: 20px;
  font-weight: bold;
  color: black;
  background-color: white;
  resize: none;
}

textarea::placeholder {
    color: #057BBD; /* 可以根据需要设置颜色 */
    font-style: italic; /* 斜体 */
    font-size: 16px;
  }

textarea::-webkit-scrollbar {
      width: 10px; /* 滚动条宽度 */
    }

textarea::-webkit-scrollbar-thumb {
  background-color: #021E43; /* 滚动条滑块颜色 */
}

textarea::-webkit-scrollbar-track {
  background-color: #1BADDC; /* 滚动条轨道颜色 */
}

.MAC,.NCS{
  background-color: #82B0D2;
}

.DSM,.DSD{
  background-color: #FFBE7A;
}

.NA,.TT,.TI{
  background-color: #FA7F6F;
}

.NCM{
  background-color: #8ECFC9;
}

</style>
  