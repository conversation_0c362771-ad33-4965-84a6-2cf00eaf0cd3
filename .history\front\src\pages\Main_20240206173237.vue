<template>
    <div class="main-view">
        <a-button @click="goToLogin" type="default" title="退出登录"><LoginOutlined   :style="{ fontSize: '24px' }"/></a-button>
        <div class="main-back"><UserOutlined   :style="{ fontSize: '22px' }"/>{{ $store.state.username }}</div>
      <div class="main-view-btns">
        <a-button type="dashed" @click="goToUserManagement" class="Admin"><ContactsFilled  :style="{fontSize:'32px'}"/>个人信息</a-button>
        <a-button type="dashed" @click="goToTaskManagement" class="Admin"><ProfileFilled :style="{fontSize:'32px'}"/>会话管理</a-button>
        </div>
    </div>
  </template>
  
  <script>
    import { ContactsFilled ,ProfileFilled,UserOutlined,LoginOutlined} from '@ant-design/icons-vue';
  export default {
    name: 'MainView',
    components: {
        ContactsFilled,
        ProfileFilled,
        UserOutlined,
        LoginOutlined
    },
    methods: {
      goToUserManagement() {
        // 在这里添加导航到用户管理的逻辑
        console.log('跳转到用户管理页面');
        this.$router.push('/user');
      },
      goToTaskManagement() {
        // 在这里添加导航到任务管理的逻辑
        console.log('跳转到任务管理页面');
        this.$router.push('/task');
      },
    goToLogin() {
        // 在这里添加导航到登录页面的逻辑
        this.$router.push('/');
    }
    },
  };
  </script>
  
  <style>
  .main-view {
    margin: 0 auto;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #ffffff;
    opacity: 0.99;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .main-view > .ant-btn:first-child {
    /* 左对齐 */
    position: absolute;
    left: 0;
    top: 0;
    background-color: #ffffff;
    border: 0px;
    margin: 10px;
  }
.main-view-btns {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .Admin {
    width: 100%;
    font-size: 28px;
    height: 60px;
    width: 200px;
    margin: 20px;
    background-color: rgb(190, 200, 200);
    letter-spacing: 1px;
  }
.main-back {
    background-color: #ffffff;
    border: 0px;
    height: 50px;
    font-size: 20px;
    width: 200px;
    margin-top: 60px;
    z-index: 999;
  }
  .main-back > .anticon {
    margin-right: 10px;
  }
  /* 在这里可以添加样式，也可以使用Ant Design Vue提供的样式 */
  </style>
  