// store/index.js
import Vuex from 'vuex';


export default new Vuex.Store({
  state: {
    username: '',
    password: '',
    email: '',
    is_admin: false,
    task_record:{},
    uploading: false,
    scanning: false,

    task_ready: false,
  },
  mutations: {
    updateUsername(state, newData) {
      state.username = newData;
    },
    updatePassword(state, newData) {
      state.password = newData;
    },
    updateEmail(state, newData) {
      state.email = newData;
    },
    updateIsAdmin(state, newData) {
      state.is_admin = newData;
    },
    updateTaskRecord(state, newData) {
      // 字典赋值
      state.task_record = Object.assign({}, state.task_record, newData);
      console.log(state.task_record);
    },
    updateUploading(state, newData) {
      state.uploading = newData;
    },
    updateScanning(state, newData) {
      state.scanning = newData;
    },
    updateTaskReady(state, newData) {
      state.task_ready = newData;
    }
  },
  actions: {
    updateUsername(context, newData) {
      context.commit('updateUsername', newData);
    },
    updatePassword(context, newData) {
      context.commit('updatePassword', newData);
    },
    updateEmail(context, newData) {
      context.commit('updateEmail', newData);
    },
    updateIsAdmin(context, newData) {
      context.commit('updateIsAdmin', newData);
    },
    updateTaskRecord(context, newData) {
      context.commit('updateTaskRecord', newData);
    },
    updateUploading(context, newData) {
      context.commit('updateUploading', newData);
    },
    updateScanning(context, newData) {
      context.commit('updateScanning', newData);
    },
    updateTaskReady(context, newData) {
      context.commit('updateTaskReady', newData);
    }
  }
});
