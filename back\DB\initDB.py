from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# 创建一个基类
Base = declarative_base()
# 定义用户表
class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String, unique=True, nullable=False)
    password = Column(String, nullable=False)
    email = Column(String, nullable=False)
    is_admin = Column(Boolean, default=False)

# 定义任务表
class Task(Base):
    __tablename__ = 'tasks'

    id = Column(Integer, primary_key=True)
    username = Column(String, nullable=False)
    task_number = Column(String, nullable=False)
    update_date = Column(DateTime, default=datetime.utcnow)
    create_date = Column(DateTime, default=datetime.utcnow)
    task_mode = Column(String)
    # 任务模式：efficient, accurate, balanced
    task_status = Column(String)
    # 任务状态：preparing, running, finished
    task_config = Column(String)
    task_description = Column(String)

if __name__ == '__main__':
    os.chdir(os.path.dirname(__file__))

    # 先删除原有的数据库文件
    if os.path.exists('database.db'):
        os.remove('database.db')

    # 创建一个引擎，连接到SQLite数据库文件
    engine = create_engine('sqlite:///database.db', echo=True)

    # 创建表格
    Base.metadata.create_all(engine)

    # 创建一个会话
    Session = sessionmaker(bind=engine)
    session = Session()

    # 插入初始管理员账户
    admin_user = User(username='admin',password='123456',email='<EMAIL>',is_admin=True)
    session.add(admin_user)
    session.commit()

    # 关闭会话
    session.close()
